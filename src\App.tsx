import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { useEffect } from "react";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import Layout from "@/components/Layout";
import ProtectedRoute from "@/components/ProtectedRoute";
import { CookieConsentBanner } from "@/components/ui/cookie-consent";
import { Analytics } from "@vercel/analytics/react";
import { WelcomePopup } from "@/components/WelcomePopup";

// Feature imports
import { HomePage } from "@/features/home";
import { AuthPage } from "@/features/auth";
import { ProfilePage } from "@/features/profile";
import { MembersPage, MemberDetailPage } from "@/features/members";
import { BusinessDirectoryPage, BusinessDetailPage, BusinessManagementPage } from "@/features/businessDirectory";
import BusinessProductsPage from "@/features/businessDirectory/pages/BusinessProductsPage";
import BusinessImagesPage from "@/features/businessDirectory/pages/BusinessImagesPage";
import { ProductsDirectoryPage, ProductDetailPage } from "@/features/products";
import LogoDisplayDemo from "@/features/businessDirectory/components/LogoDisplayDemo";
import { EventsDirectoryPage, EventDetailPage, EventManagementPage } from "@/features/events";
import { FundingFinderPage, FundingDetailPage } from "@/features/fundingFinder";
import { JobsPage } from "@/features/jobs";
import { SocialFeedPage, SocialPostDetailPage } from "@/features/social";
import { ResourceDirectoryPage, UserResourcesPage } from "@/features/resources/pages";
import ResourceDetailPage from "@/features/resources/pages/ResourceDetailPage";
import SimpleKeepingAnEyeOnPage from "@/features/curatedBusinesses/pages/SimpleKeepingAnEyeOnPage";
import CuratedBusinessForm from "@/features/curatedBusinesses/components/CuratedBusinessForm";


// Shared pages
import NotFoundPage from "./pages/NotFoundPage";
import TestIndex from "./pages/testindex";
import ComingSoonPage from "./pages/ComingSoonPage";
import PrivacyPolicyPage from "./pages/PrivacyPolicyPage";
import TermsOfServicePage from "./pages/TermsOfServicePage";
import CookieSettingsPage from "./pages/CookieSettingsPage";
import GettingTheMostPage from "./pages/GettingTheMostPage";
import URLTestPage from "./pages/URLTestPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import TestPasswordResetPage from "./pages/TestPasswordResetPage";
import CategoryButtonDemoPage from "./pages/CategoryButtonDemoPage";
import StickyTestPage from "./pages/StickyTestPage";
import IsolatedStickyTest from "./pages/IsolatedStickyTest";
import SafariTestPage from "./pages/SafariTestPage";
import Model3DTestPage from "./pages/Model3DTestPage";

const queryClient = new QueryClient();

// Scroll to top component
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
};

// App content component that can use the auth context
const AppContent = () => {
  const { showWelcomePopup, setShowWelcomePopup } = useAuth();

  return (
    <>
      <ScrollToTop />
      <Routes>
        {/* Isolated routes - no Layout wrapper */}
        <Route path="/isolated-sticky-test" element={<IsolatedStickyTest />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />

        {/* All other routes with Layout */}
        <Route path="*" element={
          <Layout>
            <Routes>
          {/* Core Routes */}
          <Route path="/" element={<HomePage />} />
          <Route path="/auth" element={<AuthPage />} />
          <Route path="/profile" element={<ProfilePage />} />
          <Route path="/members" element={<MembersPage />} />
          <Route path="/members/:memberId" element={<MemberDetailPage />} />
          
          {/* Business Directory Routes */}
          <Route path="/business-directory" element={<BusinessDirectoryPage />} />
          <Route path="/keeping-an-eye-on" element={<SimpleKeepingAnEyeOnPage />} />
          <Route path="/admin/curated-businesses/new" element={
            <ProtectedRoute>
              <CuratedBusinessForm mode="create" />
            </ProtectedRoute>
          } />
          <Route path="/admin/curated-businesses/edit/:id" element={
            <ProtectedRoute>
              <CuratedBusinessForm mode="edit" />
            </ProtectedRoute>
          } />
          <Route path="/business/:businessId" element={<BusinessDetailPage />} />
          <Route path="/business/:businessId/products" element={<BusinessProductsPage />} />
          <Route path="/business/:businessId/images" element={<BusinessImagesPage />} />
          <Route path="/business-management" element={<BusinessManagementPage />} />

          {/* Products Directory Routes */}
          <Route path="/products" element={<ProductsDirectoryPage />} />
          <Route path="/products/:productId" element={<ProductDetailPage />} />
          
          {/* Test Routes */}
          <Route path="/test" element={<TestIndex />} />
          <Route path="/url-test" element={<URLTestPage />} />
          <Route path="/sticky-test" element={<StickyTestPage />} />
          <Route path="/safari-test" element={<SafariTestPage />} />
          <Route path="/3d-model-test" element={<Model3DTestPage />} />
          <Route path="/forgot-password" element={<ForgotPasswordPage />} />
          <Route path="/test-password-reset" element={<TestPasswordResetPage />} />
          <Route path="/category-button-demo" element={<CategoryButtonDemoPage />} />
          <Route path="/logo-demo" element={<LogoDisplayDemo />} />
          
          {/* Feature Routes */}
          <Route path="/funding" element={<FundingFinderPage />} />
          <Route path="/funding/:fundingId" element={<FundingDetailPage />} />

          {/* Events Routes */}
          <Route path="/events" element={<EventsDirectoryPage />} />
          <Route path="/events/:eventId" element={<EventDetailPage />} />
          <Route path="/event-management" element={
            <ProtectedRoute>
              <EventManagementPage />
            </ProtectedRoute>
          } />

          {/* Legal Pages */}
          <Route path="/privacy-policy" element={<PrivacyPolicyPage />} />
          <Route path="/terms-of-service" element={<TermsOfServicePage />} />
          <Route path="/cookie-settings" element={<CookieSettingsPage />} />
          <Route path="/getting-the-most" element={<GettingTheMostPage />} />

          {/* Social Routes */}
          <Route path="/social" element={<SocialFeedPage />} />
          <Route path="/social/post/:postId" element={<SocialPostDetailPage />} />

          {/* Resource Library Routes */}
          <Route path="/resources" element={<ResourceDirectoryPage />} />
          <Route path="/resources/:resourceId" element={<ResourceDetailPage />} />
          <Route path="/my-resources" element={
            <ProtectedRoute>
              <UserResourcesPage />
            </ProtectedRoute>
          } />

          {/* Coming Soon Routes */}
          <Route path="/education" element={<ComingSoonPage />} />
          <Route path="/jobs" element={<ComingSoonPage />} />
          
              {/* Catch-all Route */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Layout>
        } />
      </Routes>
      <WelcomePopup
        isOpen={showWelcomePopup}
        onClose={() => setShowWelcomePopup(false)}
      />
      <CookieConsentBanner />
      <Analytics />
    </>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <AppContent />
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
