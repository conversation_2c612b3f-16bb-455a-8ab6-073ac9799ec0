import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, X, Filter, ArrowUpDown, Leaf, Building2, Tag as TagIcon, Star } from "lucide-react";
import { CategoryButton } from '@/components/ui/category-button';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { UKIndustryService } from '@/services/ukIndustryService';
import { TagService } from '@/services/tagService';
import type { Tag } from '@/types/tags.types';
import type { NetZeroCategoryWithSubcategories } from '@/types/netzero-categories.types';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';

// Sort options
export type CuratedBusinessSortOption = 'newest' | 'oldest' | 'featured' | 'alphabetical';

// Filter state interface
export interface CuratedBusinessFilterState {
  showFilters: boolean;
  searchTerm: string;
  selectedTags: string[];
  selectedCategories: string[];
  selectedIndustries: string[];
  sortBy: CuratedBusinessSortOption;
  featuredOnly: boolean;
}

interface CuratedBusinessFiltersProps {
  filters: CuratedBusinessFilterState;
  onFiltersChange: (filters: CuratedBusinessFilterState) => void;
  totalBusinesses: number;
  filteredCount: number;
  industries?: UKIndustryWithChildren[];
}

const CuratedBusinessFilters: React.FC<CuratedBusinessFiltersProps> = ({
  filters,
  onFiltersChange,
  totalBusinesses,
  filteredCount,
  industries = []
}) => {
  // Filter helper functions
  const updateFilters = (updates: Partial<CuratedBusinessFilterState>) => {
    onFiltersChange({
      ...filters,
      ...updates
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      showFilters: false,
      searchTerm: '',
      selectedTags: [],
      selectedCategories: [],
      selectedIndustries: [],
      sortBy: 'newest',
      featuredOnly: false
    });
  };

  const hasActiveFilters = () => {
    return filters.searchTerm ||
           (filters.selectedTags && filters.selectedTags.length > 0) ||
           (filters.selectedCategories && filters.selectedCategories.length > 0) ||
           (filters.selectedIndustries && filters.selectedIndustries.length > 0) ||
           filters.featuredOnly;
  };

  return (
    <>
      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search businesses by name, description, or URL..."
                  value={filters.searchTerm}
                  onChange={(e) => updateFilters({ searchTerm: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Sort */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <ArrowUpDown className="w-4 h-4 text-muted-foreground" />
                <Select value={filters.sortBy} onValueChange={(value: CuratedBusinessSortOption) => updateFilters({ sortBy: value })}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">Newest</SelectItem>
                    <SelectItem value="oldest">Oldest</SelectItem>
                    <SelectItem value="featured">Featured</SelectItem>
                    <SelectItem value="alphabetical">A-Z</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Results and Filter Toggle */}
              <div className="flex items-center justify-between lg:justify-end gap-4">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredCount} of {totalBusinesses} businesses
                </div>
                <Button
                  variant={filters.showFilters ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilters({ showFilters: !filters.showFilters })}
                  className={`${filters.showFilters ? "bg-blue-600 hover:bg-blue-700" : ""} min-w-[120px]`}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  <span className="hidden xs:inline">{filters.showFilters ? 'Hide' : 'Show'} </span>Filters
                  {hasActiveFilters() && (
                    <Badge variant="secondary" className="ml-2 bg-white text-blue-600 text-xs">
                      {(filters.selectedTags || []).length +
                       (filters.selectedCategories || []).length +
                       (filters.selectedIndustries || []).length +
                       (filters.featuredOnly ? 1 : 0)}
                    </Badge>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>

        {/* Filter Panels */}
        {filters.showFilters && (
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Featured Filter */}
              <div className="flex items-center gap-2">
                <CategoryButton
                  categoryId="featured"
                  categoryName="Featured Only"
                  theme={{
                    icon: Star,
                    textColor: "text-yellow-700",
                    borderColor: "border-yellow-200",
                    hoverBgColor: "hover:bg-yellow-200",
                    selectedBgColor: "bg-yellow-100",
                    selectedBorderColor: "border-yellow-300"
                  }}
                  isSelected={filters.featuredOnly}
                  onClick={() => updateFilters({ featuredOnly: !filters.featuredOnly })}
                  variant="dynamic-button"
                  size="xs"
                  showCount={false}
                  className="w-fit"
                />
              </div>

              {/* Clear All Button */}
              {hasActiveFilters() && (
                <div className="pt-4 border-t">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={clearAllFilters}
                    className="w-full gap-2"
                  >
                    <X className="h-4 w-4" />
                    Clear All Filters
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        )}
      </Card>

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <Card className="mb-6">
          <CardContent className="py-4">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium text-muted-foreground">Active filters:</span>

              {/* Search term filter */}
              {filters.searchTerm && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Search className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Search: {filters.searchTerm}</span>
                  <button
                    onClick={() => updateFilters({ searchTerm: '' })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Featured filter */}
              {filters.featuredOnly && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Star className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Featured Only</span>
                  <button
                    onClick={() => updateFilters({ featuredOnly: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Industry filters */}
              {filters.selectedIndustries.map((industryId) => {
                const industry = industries.flatMap(p => p.children || []).find(c => c.id === industryId);
                const parentIndustry = industries.find(p => p.children?.some(c => c.id === industryId));
                const theme = parentIndustry ? getIndustryTheme(parentIndustry.name) : null;
                return industry ? (
                  <Badge
                    key={industryId}
                    variant="outline"
                    className={`flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit border-2 ${theme?.selectedBgColor || 'bg-gray-50'} ${theme?.iconColor || 'text-gray-700'}`}
                    style={theme?.topBorderStyle}
                  >
                    <div className="flex items-center gap-1">
                      <IndustryIcon
                        industryName={parentIndustry?.name || industry.name}
                        className="w-3 h-3 flex-shrink-0"
                      />
                      <span className="whitespace-nowrap">{industry.name}</span>
                    </div>
                    <button
                      onClick={() => updateFilters({
                        selectedIndustries: filters.selectedIndustries.filter(id => id !== industryId)
                      })}
                      className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ) : null;
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default CuratedBusinessFilters;