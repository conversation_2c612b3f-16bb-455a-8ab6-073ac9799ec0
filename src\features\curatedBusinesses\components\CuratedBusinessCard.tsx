import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ExternalLink,
  Star,
  ChevronDown,
  ChevronUp,
  Building2,
  Leaf,
  Edit,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import URLPreview from '@/features/social/components/URLPreview';
import TagDisplay from '@/components/tags/TagDisplay';
import { NetZeroCategoryIcon, getNetZeroCategoryTheme } from '@/utils/netZeroCategoryIcons';
import { IndustryIcon, getIndustryTheme } from '@/utils/industryIcons';

import type { Tag } from '@/types/tags.types';

export interface CuratedBusinessCardData {
  id: string;
  business_name: string;
  description?: string;
  website_url: string;
  featured: boolean;
  url_title?: string;
  url_description?: string;
  url_image?: string;
  url_site_name?: string;
  url_favicon?: string;
  admin_notes?: string;
  created_at: string;
  tags?: Tag[];
  categories?: Array<{
    id: string;
    name: string;
    slug: string;
    category?: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  industries?: Array<{
    id: string;
    name: string;
    slug: string;
    parent?: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  industry?: {
    id: string;
    name: string;
    slug: string;
    parent?: {
      id: string;
      name: string;
      slug: string;
    };
  };
}

interface CuratedBusinessCardProps {
  business: CuratedBusinessCardData;
  onTagClick?: (tagName: string) => void;
  onCategoryClick?: (categoryId: string) => void;
  onIndustryClick?: (industryId: string) => void;
  onEdit?: (business: CuratedBusinessCardData) => void;
  onDelete?: (businessId: string) => void;
  className?: string;
  compact?: boolean;
}

interface CategoriesIndustriesDisplayProps {
  categories: Array<{
    id: string;
    name: string;
    slug: string;
    category?: {
      name: string;
      slug: string;
    };
  }>;
  industries: Array<{
    id: string;
    name: string;
    slug: string;
    parent?: {
      name: string;
      slug: string;
    };
  }>;
  onCategoryClick?: (categoryId: string) => void;
  onIndustryClick?: (industryId: string) => void;
  compact?: boolean;
}

const CategoriesIndustriesDisplay: React.FC<CategoriesIndustriesDisplayProps> = ({
  categories = [],
  industries = [],
  onCategoryClick,
  onIndustryClick,
  compact
}) => {
  const [showAll, setShowAll] = useState(false);
  const maxVisible = compact ? 1 : 2; // Show 1-2 items initially

  // Ensure categories and industries are arrays and filter out invalid items
  const validCategories = Array.isArray(categories) ? categories.filter(cat => cat && cat.id) : [];
  const validIndustries = Array.isArray(industries) ? industries.filter(ind => ind && ind.id) : [];

  const allItems = [
    ...validCategories.map(cat => ({ type: 'category', item: cat })),
    ...validIndustries.map(ind => ({ type: 'industry', item: ind }))
  ];

  const visibleItems = showAll ? allItems : allItems.slice(0, maxVisible);
  const hasMore = allItems.length > maxVisible;

  return (
    <div className={cn("mt-3", compact && "mt-2")}>
      <div className="flex flex-wrap gap-2">
        {visibleItems.map(({ type, item }, index) => (
          <Badge
            key={`${type}-${item.id}`}
            variant="outline"
            className={cn(
              "text-xs cursor-pointer hover:bg-primary/5 transition-colors",
              compact ? "px-2 py-0.5" : "px-2 py-1",
              type === 'category' ? "border-green-200 text-green-700 hover:bg-green-50" : "border-blue-200 text-blue-700 hover:bg-blue-50"
            )}
            onClick={() => {
              if (type === 'category') {
                onCategoryClick?.(item.id);
              } else {
                onIndustryClick?.(item.id);
              }
            }}
          >
            {type === 'category' ? (
              <>
                {NetZeroCategoryIcon && (
                  <NetZeroCategoryIcon
                    categoryName={item.category?.name || 'Unknown'}
                    size="sm"
                    className="w-3 h-3 mr-1"
                  />
                )}
                {item.name || 'Unknown Category'}
              </>
            ) : (
              <>
                {IndustryIcon && (
                  <IndustryIcon
                    industryName={item.parent?.name || item.name || 'Unknown'}
                    size="sm"
                    className="w-3 h-3 mr-1"
                  />
                )}
                {item.name || 'Unknown Industry'}
              </>
            )}
          </Badge>
        ))}

        {hasMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAll(!showAll)}
            className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground transition-colors"
          >
            {showAll ? (
              <>
                <ChevronUp size={12} className="mr-1" />
                Show less
              </>
            ) : (
              <>
                <ChevronDown size={12} className="mr-1" />
                +{allItems.length - maxVisible} more
              </>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

const CuratedBusinessCard: React.FC<CuratedBusinessCardProps> = ({
  business,
  onTagClick,
  onCategoryClick,
  onIndustryClick,
  onEdit,
  onDelete,
  className,
  compact = false
}) => {
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';
  const isOwner = user?.id === business.created_by;
  const canEdit = isAdmin || isOwner;
  const businessName = business.url_title || business.business_name || 'Untitled Business';
  const description = business.url_description || business.description;
  
  // Combine categories array with single industry
  const allCategories = business.categories || [];
  const allIndustries = business.industries || (business.industry ? [business.industry] : []);

  return (
    <Card className={cn("h-full flex flex-col hover:shadow-lg transition-shadow duration-200", className)}>
      <CardHeader className={cn("pb-3", compact && "pb-2")}>
        <div className="flex items-start justify-between">
          <CardTitle className={cn("text-lg leading-tight", compact && "text-base")}>
            <div className="flex items-center gap-2">
              {business.featured && (
                <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 flex-shrink-0" />
              )}
              <span className="line-clamp-2">{businessName}</span>
            </div>
          </CardTitle>
          
          {/* Action buttons */}
          <div className="flex items-center gap-1 ml-3">
            {canEdit && onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(business)}
                className="h-8 w-8 p-0"
              >
                <Edit size={14} />
              </Button>
            )}

            {canEdit && onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(business.id)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
              >
                <Trash2 size={14} />
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              asChild
              className="flex-shrink-0 h-8 w-8 p-0"
            >
              <a
                href={business.website_url}
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Visit website"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          </div>
        </div>

        {/* Categories and Industries */}
        {(allCategories.length > 0 || allIndustries.length > 0) && (
          <CategoriesIndustriesDisplay
            categories={allCategories}
            industries={allIndustries}
            onCategoryClick={onCategoryClick}
            onIndustryClick={onIndustryClick}
            compact={compact}
          />
        )}
      </CardHeader>

      <CardContent className={cn("space-y-4 flex-1 flex flex-col", compact && "space-y-3 px-4 pb-4")}>
        {/* URL Preview */}
        <div className="flex-1">
          <URLPreview url={business.website_url} showPreview={true} />
        </div>

        {/* Description */}
        {description && (
          <div className={cn("text-sm text-muted-foreground", compact && "text-xs")}>
            <p className={compact ? "line-clamp-3" : "line-clamp-5"}>{description}</p>
          </div>
        )}

        {/* Tags */}
        {business.tags && business.tags.length > 0 && (
          <div className={cn("pt-2 border-t border-gray-100 flex-shrink-0", compact && "pt-1")}>
            <TagDisplay
              tags={business.tags}
              maxTags={compact ? 3 : 4}
              size="sm"
              variant="outline"
              clickable={true}
              onTagClick={(tag) => onTagClick?.(tag.name)}
            />
          </div>
        )}

        {/* Admin Notes (only visible to admins - could be controlled by prop) */}
        {business.admin_notes && (
          <div className="pt-2 border-t border-gray-100 text-xs text-muted-foreground bg-gray-50 p-2 rounded">
            <strong>Admin notes:</strong> {business.admin_notes}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CuratedBusinessCard;