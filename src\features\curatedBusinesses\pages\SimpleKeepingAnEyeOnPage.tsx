import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Building2, Eye, Plus, Search } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useAdmin } from '@/hooks/useAdmin';
import { CuratedBusinessService, type CuratedBusiness } from '../services/curatedBusinessService';
import CuratedBusinessFilters, { type CuratedBusinessFilterState } from '../components/CuratedBusinessFilters';
import CuratedBusinessCard from '../components/CuratedBusinessCard';
import { CategoryCardsDemoStyle } from '@/components/shared/CategoryCardsDemoStyle';
import { IndustryCategoryCardsDemoStyle } from '@/components/shared/IndustryCategoryCardsDemoStyle';
import { UKIndustryService } from '@/services/ukIndustryService';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';

const SimpleKeepingAnEyeOnPage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { isAdmin } = useAdmin();
  
  const [businesses, setBusinesses] = useState<CuratedBusiness[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [industryHierarchy, setIndustryHierarchy] = useState<UKIndustryWithChildren[]>([]);

  // Filter state
  const [filters, setFilters] = useState<CuratedBusinessFilterState>({
    showFilters: false,
    searchTerm: '',
    selectedTags: [],
    selectedCategories: [],
    selectedIndustries: [],
    sortBy: 'newest',
    featuredOnly: false
  });

  const loadBusinesses = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await CuratedBusinessService.getAllBusinesses();
      setBusinesses(data);
    } catch (err) {
      console.error('Failed to load businesses:', err);
      setError('Failed to load businesses');
    } finally {
      setLoading(false);
    }
  };

  const loadIndustryHierarchy = async () => {
    try {
      const industries = await UKIndustryService.getAllIndustriesWithChildren();
      setIndustryHierarchy(industries);
    } catch (error) {
      console.error('Error loading industry hierarchy:', error);
    }
  };

  useEffect(() => {
    loadBusinesses();
    loadIndustryHierarchy();
  }, []);

  // Filter and sort businesses
  const filteredAndSortedBusinesses = useMemo(() => {
    let filtered = businesses;

    // Search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(business => 
        (business.business_name || business.url_title || '').toLowerCase().includes(searchLower) ||
        (business.description || business.url_description || '').toLowerCase().includes(searchLower) ||
        business.website_url.toLowerCase().includes(searchLower) ||
        business.tags?.some(tag => tag.name?.toLowerCase().includes(searchLower))
      );
    }

    // Featured filter
    if (filters.featuredOnly) {
      filtered = filtered.filter(business => business.featured);
    }

    // Tag filter
    if (filters.selectedTags.length > 0) {
      filtered = filtered.filter(business =>
        business.tags?.some(tag => filters.selectedTags.includes(tag.id))
      );
    }

    // Category filter (filter by parent category names to match card generation)
    if (filters.selectedCategories.length > 0) {
      filtered = filtered.filter(business => {
        if (filters.selectedCategories.includes('not-classified')) {
          return !business.categories || business.categories.length === 0;
        }

        return business.categories?.some(subcategory => {
          const parentCategoryName = subcategory.category?.name;
          if (!parentCategoryName) return false;
          const parentCategoryId = String(parentCategoryName).toLowerCase().replace(/\s+/g, '-');
          return filters.selectedCategories.includes(parentCategoryId);
        });
      });
    }

    // Industry filter
    if (filters.selectedIndustries.length > 0) {
      filtered = filtered.filter(business => {
        // Handle special case for "not-classified"
        if (filters.selectedIndustries.includes('not-classified')) {
          return !business.industries || business.industries.length === 0;
        }

        // Check if business has any industry that matches the selected industries
        // (selectedIndustries now contains parent + all children IDs)
        return business.industries?.some(industry =>
          filters.selectedIndustries.includes(industry.id)
        );
      });
    }

    // Sort
    return filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'featured':
          if (a.featured && !b.featured) return -1;
          if (!a.featured && b.featured) return 1;
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'oldest':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'alphabetical':
          const nameA = (a.url_title || a.business_name || '').toLowerCase();
          const nameB = (b.url_title || b.business_name || '').toLowerCase();
          return nameA.localeCompare(nameB);
        default: // newest
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });
  }, [businesses, filters]);

  // Helper functions
  const hasActiveFilters = () => {
    return filters.searchTerm || 
           filters.selectedTags.length > 0 || 
           filters.selectedCategories.length > 0 ||
           filters.selectedIndustries.length > 0 ||
           filters.featuredOnly;
  };

  const handleTagFilter = (tagName: string) => {
    // Find the tag by name and add to filters
    const business = businesses.find(b => b.tags?.some(t => t.name === tagName));
    const tag = business?.tags?.find(t => t.name === tagName);
    if (tag) {
      setFilters(prev => ({
        ...prev,
        selectedTags: [tag.id],
        selectedCategories: [],
        selectedIndustries: []
      }));
    }
  };

  const handleCategoryFilter = (categoryId: string) => {
    setFilters(prev => ({
      ...prev,
      selectedCategories: [categoryId],
      selectedTags: [],
      selectedIndustries: []
    }));
  };

  const handleIndustryFilter = (industryId: string) => {
    // Find the selected industry in the hierarchy
    const selectedIndustry = industryHierarchy.find(ind => ind.id === industryId);

    if (selectedIndustry) {
      // Include parent + all children in the selected industries
      const allIndustryIds = [industryId, ...(selectedIndustry.children?.map(child => child.id) || [])];

      setFilters(prev => ({
        ...prev,
        selectedIndustries: allIndustryIds,
        selectedTags: [],
        selectedCategories: []
      }));
    } else {
      // Fallback for special cases like 'not-classified'
      setFilters(prev => ({
        ...prev,
        selectedIndustries: [industryId],
        selectedTags: [],
        selectedCategories: []
      }));
    }
  };

  const handleEditBusiness = (business: any) => {
    // Navigate to admin edit form
    navigate(`/admin/curated-businesses/edit/${business.id}`);
  };

  const handleDeleteBusiness = async (businessId: string) => {
    if (window.confirm('Are you sure you want to delete this business? This action cannot be undone.')) {
      try {
        await CuratedBusinessService.deleteBusiness(businessId);
        // Reload the list
        loadBusinesses();
        // Show success message
        console.log('Business deleted successfully');
      } catch (error) {
        console.error('Failed to delete business:', error);
        // You could add a toast notification here
      }
    }
  };

  // Stats
  const stats = {
    total: (businesses || []).length,
    featured: (businesses || []).filter(b => b.featured).length,
    uniqueIndustries: new Set((businesses || []).flatMap(b => (b.industries || []).map(i => i.id))).size,
    uniqueCategories: new Set((businesses || []).flatMap(b => (b.categories || []).map(c => c.id))).size
  };

  // Dynamically generate Net Zero categories and counts from filteredAndSortedBusinesses
  const netZeroCategoryMap = new Map();
  let notClassifiedCount = 0;
  filteredAndSortedBusinesses.forEach(business => {
    if (business.categories && business.categories.length > 0) {
      // Get unique parent categories for this business
      const parentCategories = new Set();
      (business.categories || []).forEach(subcat => {
        const catName = subcat.category?.name || 'Unknown';
        parentCategories.add(catName);
      });
      
      // Count this business once for each parent category it belongs to
      parentCategories.forEach(catName => {
        if (!netZeroCategoryMap.has(catName)) {
          netZeroCategoryMap.set(catName, {
            id: String(catName).toLowerCase().replace(/\s+/g, '-'),
            name: catName,
            count: 0
          });
        }
        netZeroCategoryMap.get(catName).count++;
      });
    } else {
      notClassifiedCount++;
    }
  });
  const netZeroCategoriesDynamic = Array.from(netZeroCategoryMap.values());
  if (notClassifiedCount > 0) {
    netZeroCategoriesDynamic.push({
      id: 'not-classified',
      name: 'Not Classified',
      description: 'Businesses without specific Net Zero categories',
      count: notClassifiedCount
    });
  }

  // Dynamically generate industries and counts
  const industryMap = new Map();
  let allIndustriesCount = 0;

  filteredAndSortedBusinesses.forEach(business => {
    if (business.applies_to_all_industries) {
      allIndustriesCount++;
    } else if (business.industries && business.industries.length > 0) {
      // Get unique industries for this business (use the actual industry names)
      const industryNames = new Set();
      business.industries.forEach(ind => {
        const indName = ind.name || 'Unknown';
        industryNames.add(indName);

      });
      
      // Count this business once for each parent industry it belongs to
      industryNames.forEach(indName => {
        if (!industryMap.has(indName)) {
          industryMap.set(indName, {
            id: String(indName).toLowerCase().replace(/\s+/g, '-'),
            name: indName,
            count: 0
          });
        }
        industryMap.get(indName).count++;

      });
    }
  });
  const industriesDynamic = Array.from(industryMap.values()).map(industry => ({
    ...industry,
    children: [] // Add empty children array for compatibility
  }));

  if (allIndustriesCount > 0) {
    industriesDynamic.push({
      id: 'all-industries',
      name: 'All Industries',
      description: 'Businesses that apply to all industries',
      count: allIndustriesCount,
      children: []
    });
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading businesses...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center py-12">
            <p className="text-red-600">{error}</p>
            <Button onClick={loadBusinesses} className="mt-4">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Eye className="h-8 w-8 text-blue-600" />
                Keeping an Eye On
              </h1>
              <p className="mt-2 text-lg text-gray-600">
                Businesses that neXzero thinks are worthy of keeping an eye on and checking out.
              </p>
              <p className="mt-1 text-sm text-gray-500">
                Want to be featured here? {' '}
                <button
                  onClick={() => navigate('/business-directory/register')}
                  className="text-blue-600 hover:text-blue-700 underline"
                >
                  Register your business
                </button>
                {' '}for a full profile with enhanced features.
              </p>
            </div>
            
            {isAdmin && (
              <Button onClick={() => navigate('/admin/curated-businesses/new')} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Business
              </Button>
            )}
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-gray-600">Total Businesses</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-yellow-600">{stats.featured}</div>
                <div className="text-sm text-gray-600">Featured</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">{stats.uniqueCategories}</div>
                <div className="text-sm text-gray-600">Categories</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">{stats.uniqueIndustries}</div>
                <div className="text-sm text-gray-600">Industries</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Net Zero Category Cards */}
        {netZeroCategoriesDynamic.length > 0 && (
          <CategoryCardsDemoStyle
            categories={netZeroCategoriesDynamic}
            type="netzero"
            title="Net Zero Categories"
            size="md"
            selectedCategoryId={filters.selectedCategories[0] || ''}
            onCategorySelect={handleCategoryFilter}
          />
        )}

        {/* Industry Category Cards */}
        {industryHierarchy.length > 0 && (
          <IndustryCategoryCardsDemoStyle
            industries={industryHierarchy}
            items={businesses.map(business => ({
              ...business,
              // Transform to match what the component expects for business type
              mainIndustry: business.industries?.[0] ? { id: business.industries[0].id } : null,
              targetIndustries: business.industries?.map(ind => ({ id: ind.id })) || []
            }))}
            type="business"
            selectedIndustryId={filters.selectedIndustries[0] || ''}
            onIndustrySelect={handleIndustryFilter}
          />
        )}

        {/* Filters */}
        <CuratedBusinessFilters
          filters={filters}
          onFiltersChange={setFilters}
          totalBusinesses={businesses.length}
          filteredCount={filteredAndSortedBusinesses.length}
          industries={industryHierarchy}
        />

        {/* Results */}
        {!loading && !error && filteredAndSortedBusinesses.length === 0 && (
          <div className="p-8 border border-dashed rounded-lg text-center mt-6">
            <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg">
              {hasActiveFilters() ? 'No businesses match your filters' : 'No businesses found'}
            </p>
            <p className="text-muted-foreground mt-2">
              {hasActiveFilters() ? 'Try adjusting your search terms or filters.' : 'Check back soon for new additions!'}
            </p>
          </div>
        )}

        {/* Business Grid */}
        {filteredAndSortedBusinesses.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
            {(filteredAndSortedBusinesses || []).map((business) => (
              <CuratedBusinessCard
                key={business.id}
                business={business}
                onTagClick={handleTagFilter}
                onCategoryClick={handleCategoryFilter}
                onIndustryClick={handleIndustryFilter}
                onEdit={handleEditBusiness}
                onDelete={handleDeleteBusiness}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleKeepingAnEyeOnPage;