import { supabase } from '@/integrations/supabase/client';
import { fetchURLMetadata } from '@/utils/urlUtils';
import type { Tag } from '@/types/tags.types';

// Types for curated businesses
export interface CuratedBusiness {
  id: string;
  created_by: string;
  business_name: string;
  description: string | null;
  website_url: string;
  url_title: string | null;
  url_description: string | null;
  url_image: string | null;
  url_site_name: string | null;
  url_favicon: string | null;
  location_id: string | null;
  industry_id: string | null;
  contact_email: string | null;
  contact_phone: string | null;
  linkedin_url: string | null;
  twitter_url: string | null;
  admin_notes: string | null;
  featured: boolean;
  created_at: string;
  updated_at: string;
}

export interface CuratedBusinessWithDetails extends CuratedBusiness {
  creator: {
    first_name: string | null;
    last_name: string | null;
    email: string | null;
  };
  location?: {
    id: string;
    name: string;
  };
  industry?: {
    id: string;
    name: string;
  };
  tags: Tag[];
  categories: Array<{
    id: string;
    name: string;
  }>;
}

export interface CuratedBusinessFormData {
  business_name: string;
  description?: string;
  website_url: string;
  location_id?: string;
  industry_id?: string;
  contact_email?: string;
  contact_phone?: string;
  linkedin_url?: string;
  twitter_url?: string;
  admin_notes?: string;
  featured?: boolean;
  tags: Tag[];
  categories: string[];
}

export interface CuratedBusinessFilters {
  searchTerm?: string;
  locationIds?: string[];
  industryIds?: string[];
  categoryIds?: string[];
  tagNames?: string[];
  featuredOnly?: boolean;
  sortBy?: 'newest' | 'oldest' | 'name_asc' | 'name_desc' | 'featured';
}

export interface PaginatedCuratedBusinessResponse {
  businesses: CuratedBusinessWithDetails[];
  totalCount: number;
  hasMore: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

export class CuratedBusinessService {
  /**
   * Get paginated curated businesses with filters
   */
  static async getCuratedBusinesses(
    filters: CuratedBusinessFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedCuratedBusinessResponse> {
    // Try complex query first, fall back to simple if it fails
    try {
      return await this.getCuratedBusinessesWithRelations(filters, pagination);
    } catch (error) {
      console.warn('Complex query failed, falling back to simple query:', error);
      try {
        return await this.getCuratedBusinessesSimple(filters, pagination);
      } catch (simpleError) {
        console.error('Simple query also failed:', simpleError);
        return {
          businesses: [],
          totalCount: 0,
          hasMore: false
        };
      }
    }
  }

  /**
   * Get curated businesses with full relationships (complex query)
   */
  private static async getCuratedBusinessesWithRelations(
    filters: CuratedBusinessFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedCuratedBusinessResponse> {
    const { page = 1, limit = 12 } = pagination;
    const offset = (page - 1) * limit;

    // Start with base query
    let query = supabase
      .from('curated_businesses')
      .select(`
        *,
        creator:profiles!created_by (
          first_name,
          last_name,
          contact_email
        ),
        location:locations (
          id,
          name
        ),
        industry:uk_industries (
          id,
          name
        ),
        curated_business_tags (
          tag:tags (
            id,
            name
          )
        ),
        curated_business_netzero_categories (
          category:netzero_categories (
            id,
            name
          )
        ),
        curated_business_industries (
          industry:uk_industries (
            id,
            name
          )
        )
      `, { count: 'exact' });

    // Apply filters
    if (filters.searchTerm) {
      query = query.or(`business_name.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`);
    }

    if (filters.locationIds && filters.locationIds.length > 0) {
      query = query.in('location_id', filters.locationIds);
    }

    if (filters.industryIds && filters.industryIds.length > 0) {
      query = query.in('industry_id', filters.industryIds);
    }

    if (filters.featuredOnly) {
      query = query.eq('featured', true);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'oldest':
        query = query.order('created_at', { ascending: true });
        break;
      case 'name_asc':
        query = query.order('business_name', { ascending: true });
        break;
      case 'name_desc':
        query = query.order('business_name', { ascending: false });
        break;
      case 'featured':
        query = query.order('featured', { ascending: false }).order('created_at', { ascending: false });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching curated businesses:', error);
      throw new Error('Failed to fetch curated businesses');
    }

    // Transform the data
    const businesses: CuratedBusinessWithDetails[] = (data || []).map(business => ({
      ...business,
      creator: business.creator || { first_name: null, last_name: null, email: null },
      location: business.location || undefined,
      industry: business.industry || undefined,
      tags: Array.isArray(business.curated_business_tags)
        ? business.curated_business_tags.map((bt: any) => bt.tag).filter(Boolean)
        : [],
      categories: Array.isArray(business.curated_business_netzero_categories)
        ? business.curated_business_netzero_categories.map((bc: any) => bc.category).filter(Boolean)
        : [],
      industries: Array.isArray(business.curated_business_industries)
        ? business.curated_business_industries.map((bi: any) => bi.industry).filter(Boolean)
        : []
    }));

    return {
      businesses,
      totalCount: count || 0,
      hasMore: (count || 0) > offset + limit
    };
  }

  /**
   * Get curated businesses with simple query (fallback)
   */
  private static async getCuratedBusinessesSimple(
    filters: CuratedBusinessFilters = {},
    pagination: PaginationParams = {}
  ): Promise<PaginatedCuratedBusinessResponse> {
    const { page = 1, limit = 12 } = pagination;
    const offset = (page - 1) * limit;

    // Simple query without complex relations
    let query = supabase
      .from('curated_businesses')
      .select('*', { count: 'exact' });

    // Apply basic filters
    if (filters.searchTerm) {
      query = query.or(`business_name.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`);
    }

    if (filters.locationIds && filters.locationIds.length > 0) {
      query = query.in('location_id', filters.locationIds);
    }

    if (filters.industryIds && filters.industryIds.length > 0) {
      query = query.in('industry_id', filters.industryIds);
    }

    if (filters.featuredOnly) {
      query = query.eq('featured', true);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'oldest':
        query = query.order('created_at', { ascending: true });
        break;
      case 'name_asc':
        query = query.order('business_name', { ascending: true });
        break;
      case 'name_desc':
        query = query.order('business_name', { ascending: false });
        break;
      case 'featured':
        query = query.order('featured', { ascending: false }).order('created_at', { ascending: false });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching curated businesses (simple):', error);
      throw new Error('Failed to fetch curated businesses');
    }

    // Transform the data with minimal structure
    const businesses: CuratedBusinessWithDetails[] = (data || []).map(business => ({
      ...business,
      creator: { first_name: null, last_name: null, email: null },
      location: undefined,
      industry: undefined,
      tags: [],
      categories: []
    }));

    return {
      businesses,
      totalCount: count || 0,
      hasMore: (count || 0) > offset + limit
    };
  }

  /**
   * Get a single curated business by ID
   */
  static async getCuratedBusinessById(id: string): Promise<CuratedBusinessWithDetails | null> {
    const { data, error } = await supabase
      .from('curated_businesses')
      .select(`
        *,
        creator:profiles!created_by (
          first_name,
          last_name,
          contact_email
        ),
        location:locations (
          id,
          name
        ),
        industry:uk_industries (
          id,
          name
        ),
        curated_business_tags (
          tag:tags (
            id,
            name
          )
        ),
        curated_business_netzero_categories (
          category:netzero_categories (
            id,
            name
          )
        ),
        curated_business_industries (
          industry:uk_industries (
            id,
            name
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching curated business:', error);
      return null;
    }

    if (!data) return null;

    return {
      ...data,
      creator: data.creator || { first_name: null, last_name: null, email: null },
      location: data.location || undefined,
      industry: data.industry || undefined,
      tags: Array.isArray(data.curated_business_tags)
        ? data.curated_business_tags.map((bt: any) => bt.tag).filter(Boolean)
        : [],
      categories: Array.isArray(data.curated_business_netzero_categories)
        ? data.curated_business_netzero_categories.map((bc: any) => bc.category).filter(Boolean)
        : [],
      industries: Array.isArray(data.curated_business_industries)
        ? data.curated_business_industries.map((bi: any) => bi.industry).filter(Boolean)
        : []
    };
  }

  /**
   * Create a new curated business
   */
  static async createCuratedBusiness(formData: CuratedBusinessFormData, userId: string): Promise<CuratedBusinessWithDetails> {
    // Fetch URL metadata
    let urlMetadata = null;
    try {
      urlMetadata = await fetchURLMetadata(formData.website_url);
    } catch (error) {
      console.warn('Failed to fetch URL metadata:', error);
    }

    // Create the business
    const newBusiness = {
      created_by: userId,
      business_name: formData.business_name,
      description: formData.description || null,
      website_url: formData.website_url,
      url_title: urlMetadata?.title || null,
      url_description: urlMetadata?.description || null,
      url_image: urlMetadata?.image || null,
      url_site_name: urlMetadata?.siteName || null,
      url_favicon: urlMetadata?.favicon || null,
      location_id: formData.location_id || null,
      industry_id: formData.industry_id || null,
      contact_email: formData.contact_email || null,
      contact_phone: formData.contact_phone || null,
      linkedin_url: formData.linkedin_url || null,
      twitter_url: formData.twitter_url || null,
      admin_notes: formData.admin_notes || null,
      featured: formData.featured || false
    };

    const { data: business, error } = await supabase
      .from('curated_businesses')
      .insert([newBusiness])
      .select()
      .single();

    if (error) {
      console.error('Error creating curated business:', error);
      throw new Error('Failed to create curated business');
    }

    // Add tags if provided
    if (formData.tags.length > 0) {
      const tagRelations = formData.tags.map(tag => ({
        business_id: business.id,
        tag_id: tag.id
      }));

      const { error: tagError } = await supabase
        .from('curated_business_tags')
        .insert(tagRelations);

      if (tagError) {
        console.warn('Error adding tags:', tagError);
      }
    }

    // Add categories if provided
    if (formData.categories.length > 0) {
      const categoryRelations = formData.categories.map(categoryId => ({
        business_id: business.id,
        category_id: categoryId
      }));

      const { error: categoryError } = await supabase
        .from('curated_business_netzero_categories')
        .insert(categoryRelations);

      if (categoryError) {
        console.warn('Error adding categories:', categoryError);
      }
    }

    // Fetch and return the complete business with relations
    const createdBusiness = await this.getCuratedBusinessById(business.id);
    if (!createdBusiness) {
      throw new Error('Failed to fetch created business');
    }

    return createdBusiness;
  }

  /**
   * Update a curated business
   */
  static async updateCuratedBusiness(
    id: string,
    formData: CuratedBusinessFormData
  ): Promise<CuratedBusinessWithDetails> {
    // Get current business to check if URL changed
    const currentBusiness = await this.getCuratedBusinessById(id);
    if (!currentBusiness) {
      throw new Error('Business not found');
    }

    let urlMetadata = null;
    // Only fetch new metadata if URL changed
    if (formData.website_url !== currentBusiness.website_url) {
      try {
        urlMetadata = await fetchURLMetadata(formData.website_url);
      } catch (error) {
        console.warn('Failed to fetch URL metadata:', error);
      }
    }

    // Update the business
    const updateData = {
      business_name: formData.business_name,
      description: formData.description || null,
      website_url: formData.website_url,
      location_id: formData.location_id || null,
      industry_id: formData.industry_id || null,
      contact_email: formData.contact_email || null,
      contact_phone: formData.contact_phone || null,
      linkedin_url: formData.linkedin_url || null,
      twitter_url: formData.twitter_url || null,
      admin_notes: formData.admin_notes || null,
      featured: formData.featured || false,
      ...(urlMetadata && {
        url_title: urlMetadata.title || null,
        url_description: urlMetadata.description || null,
        url_image: urlMetadata.image || null,
        url_site_name: urlMetadata.siteName || null,
        url_favicon: urlMetadata.favicon || null
      })
    };

    const { error } = await supabase
      .from('curated_businesses')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Error updating curated business:', error);
      throw new Error('Failed to update curated business');
    }

    // Update tags
    // Delete existing tags
    await supabase
      .from('curated_business_tags')
      .delete()
      .eq('business_id', id);

    // Add new tags
    if (formData.tags.length > 0) {
      const tagRelations = formData.tags.map(tag => ({
        business_id: id,
        tag_id: tag.id
      }));

      await supabase
        .from('curated_business_tags')
        .insert(tagRelations);
    }

    // Update categories
    // Delete existing categories
    await supabase
      .from('curated_business_netzero_categories')
      .delete()
      .eq('business_id', id);

    // Add new categories
    if (formData.categories.length > 0) {
      const categoryRelations = formData.categories.map(categoryId => ({
        business_id: id,
        category_id: categoryId
      }));

      await supabase
        .from('curated_business_netzero_categories')
        .insert(categoryRelations);
    }

    // Fetch and return the updated business
    const updatedBusiness = await this.getCuratedBusinessById(id);
    if (!updatedBusiness) {
      throw new Error('Failed to fetch updated business');
    }

    return updatedBusiness;
  }

  /**
   * Delete a curated business
   */
  static async deleteCuratedBusiness(id: string): Promise<void> {
    const { error } = await supabase
      .from('curated_businesses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting curated business:', error);
      throw new Error('Failed to delete curated business');
    }
  }

  /**
   * Toggle featured status
   */
  static async toggleFeatured(id: string, featured: boolean): Promise<void> {
    const { error } = await supabase
      .from('curated_businesses')
      .update({ featured })
      .eq('id', id);

    if (error) {
      console.error('Error updating featured status:', error);
      throw new Error('Failed to update featured status');
    }
  }
}