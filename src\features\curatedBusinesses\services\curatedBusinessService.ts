import { supabase } from '@/integrations/supabase/client';
import { fetchURLMetadata } from '@/utils/urlUtils';
import type { Tag } from '@/types/tags.types';

export interface CuratedBusiness {
  id: string;
  business_name: string;
  description?: string;
  website_url: string;
  admin_notes?: string;
  featured: boolean;
  applies_to_all_industries: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  url_title?: string;
  url_description?: string;
  url_image?: string;
  url_site_name?: string;
  url_favicon?: string;
  tags: Tag[];
  categories: Array<{
    id: string;
    name: string;
    slug: string;
    category?: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
  industries: Array<{
    id: string;
    name: string;
    slug: string;
    parent?: {
      id: string;
      name: string;
      slug: string;
    };
  }>;
}

export interface CuratedBusinessFormData {
  website_url: string;
  featured: boolean;
  applies_to_all_industries: boolean;
  tags: Tag[];
  categories: string[]; // Array of subcategory IDs
  industries: string[]; // Array of industry IDs (ignored if applies_to_all_industries is true)
  admin_notes: string;
}

export class CuratedBusinessService {
  /**
   * Get all curated businesses with complete data
   */
  static async getAllBusinesses(): Promise<CuratedBusiness[]> {
    try {
      // Get businesses
      const { data: businesses, error: businessError } = await supabase
        .from('curated_businesses')
        .select('*')
        .order('featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (businessError) {
        console.error('Error fetching businesses:', businessError);
        return [];
      }

      if (!businesses || businesses.length === 0) {
        return [];
      }

      // Get related data for each business
      const businessIds = businesses.map(b => b.id);
      

      const [businessTags, businessCategories, businessIndustries] = await Promise.all([
        // Get tags via junction table
        supabase
          .from('curated_business_tags')
          .select(`
            business_id,
            tag:tags (
              id,
              name
            )
          `)
          .in('business_id', businessIds),
        
        // Get categories via junction table (using subcategories like resources)
        supabase
          .from('curated_business_netzero_categories')
          .select(`
            business_id,
            subcategory:netzero_subcategories (
              id,
              name,
              category:netzero_categories (
                id,
                name
              )
            )
          `)
          .in('business_id', businessIds),
        
        // Get industries via junction table
        supabase
          .from('curated_business_industries')
          .select(`
            business_id,
            industry:uk_industries (
              id,
              name,
              parent_id
            )
          `)
          .in('business_id', businessIds)
      ]);



      // Combine data
      return businesses.map(business => ({
        ...business,
        tags: businessTags.data
          ?.filter(bt => bt.business_id === business.id)
          ?.map(bt => bt.tag)
          ?.filter(Boolean) || [],
        categories: businessCategories.data
          ?.filter(bc => bc.business_id === business.id)
          ?.map(bc => bc.subcategory ? ({
            id: bc.subcategory.id,
            name: bc.subcategory.name,
            category: {
              id: bc.subcategory.category?.id || '',
              name: bc.subcategory.category?.name || 'Unknown'
            }
          }) : null)
          ?.filter(Boolean) || [],
        industries: business.applies_to_all_industries
          ? [] // If applies to all industries, return empty array
          : (businessIndustries.data
            ?.filter(bi => bi.business_id === business.id)
            ?.map(bi => bi.industry)
            ?.filter(Boolean) || [])
      }));
    } catch (error) {
      console.error('Error in getAllBusinesses:', error);
      return [];
    }
  }

  /**
   * Get a single business by ID
   */
  static async getBusinessById(id: string): Promise<CuratedBusiness | null> {
    try {
      const { data: business, error } = await supabase
        .from('curated_businesses')
        .select('*')
        .eq('id', id)
        .single();

      if (error || !business) {
        return null;
      }

      // Get related data
      const [businessTags, businessCategories, businessIndustries] = await Promise.all([
        supabase
          .from('curated_business_tags')
          .select(`
            tag:tags (
              id,
              name
            )
          `)
          .eq('business_id', id),
        
        supabase
          .from('curated_business_netzero_categories')
          .select(`
            subcategory:netzero_subcategories (
              id,
              name,
              category:netzero_categories (
                id,
                name
              )
            )
          `)
          .eq('business_id', id),
        
        supabase
          .from('curated_business_industries')
          .select(`
            industry:uk_industries (
              id,
              name,
              parent_id
            )
          `)
          .eq('business_id', id)
      ]);

      return {
        ...business,
        tags: businessTags.data?.map(bt => bt.tag).filter(Boolean) || [],
        categories: businessCategories.data?.map(bc => bc.subcategory ? ({
          id: bc.subcategory.id,
          name: bc.subcategory.name,
          category: {
            id: bc.subcategory.category?.id || '',
            name: bc.subcategory.category?.name || 'Unknown'
          }
        }) : null).filter(Boolean) || [],
        industries: business.applies_to_all_industries
          ? []
          : (businessIndustries.data?.map(bi => bi.industry).filter(Boolean) || [])
      };
    } catch (error) {
      console.error('Error fetching business by ID:', error);
      return null;
    }
  }

  /**
   * Create a new curated business
   */
  static async createBusiness(formData: CuratedBusinessFormData): Promise<void> {
    try {
      console.log('🔧 Creating business with form data:', formData);
      
      const userId = (await supabase.auth.getUser()).data.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Fetch URL metadata
      let urlMetadata = {};
      if (formData.website_url) {
        try {
          const metadata = await fetchURLMetadata(formData.website_url);
          urlMetadata = {
            url_title: metadata.title,
            url_description: metadata.description,
            url_image: metadata.image,
            url_site_name: metadata.siteName,
            url_favicon: metadata.favicon
          };
        } catch (error) {
          console.warn('Failed to fetch URL metadata:', error);
        }
      }

      // Use URL metadata for business name and description
      const businessName = (urlMetadata as any).url_title || 'Untitled Business';
      const businessDescription = (urlMetadata as any).url_description || null;

      // Create business
      const { data: business, error: businessError } = await supabase
        .from('curated_businesses')
        .insert([{
          business_name: businessName,
          description: businessDescription,
          website_url: formData.website_url,
          admin_notes: formData.admin_notes || null,
          featured: formData.featured,
          applies_to_all_industries: formData.applies_to_all_industries,
          created_by: userId,
          ...urlMetadata
        }])
        .select()
        .single();

      if (businessError) {
        throw new Error(`Failed to create business: ${businessError.message}`);
      }

      // Add tags if any
      if (formData.tags && formData.tags.length > 0) {
        const tagInserts = formData.tags.map(tag => ({
          business_id: business.id,
          tag_id: tag.id
        }));

        const { error: tagError } = await supabase
          .from('curated_business_tags')
          .insert(tagInserts);

        if (tagError) {
          console.warn('Failed to add tags:', tagError);
        }
      }

      // Add categories if any
      if (formData.categories && formData.categories.length > 0) {
        console.log('💚 Adding categories:', formData.categories);
        const categoryInserts = formData.categories.map(subcategoryId => ({
          business_id: business.id,
          subcategory_id: subcategoryId
        }));
        console.log('💚 Category inserts:', categoryInserts);

        const { error: categoryError } = await supabase
          .from('curated_business_netzero_categories')
          .insert(categoryInserts);

        if (categoryError) {
          console.error('❌ Failed to add categories:', categoryError);
        } else {
          console.log('✅ Categories added successfully');
        }
      } else {
        console.log('⚠️ No categories to add');
      }

      // Add industries if any (only if not applies_to_all_industries)
      if (!formData.applies_to_all_industries && formData.industries && formData.industries.length > 0) {
        console.log('🏭 Adding industries:', formData.industries);
        const industryInserts = formData.industries.map(industryId => ({
          business_id: business.id,
          industry_id: industryId
        }));
        console.log('🏭 Industry inserts:', industryInserts);

        const { error: industryError } = await supabase
          .from('curated_business_industries')
          .insert(industryInserts);

        if (industryError) {
          console.error('❌ Failed to add industries:', industryError);
        } else {
          console.log('✅ Industries added successfully');
        }
      } else {
        console.log('⚠️ No industries to add or applies to all industries:', {
          applies_to_all: formData.applies_to_all_industries,
          industries: formData.industries
        });
      }
    } catch (error) {
      console.error('Error creating business:', error);
      throw error;
    }
  }

  /**
   * Update a curated business
   */
  static async updateBusiness(id: string, formData: CuratedBusinessFormData): Promise<void> {
    try {
      console.log('🔧 Updating business with form data:', formData);

      const userId = (await supabase.auth.getUser()).data.user?.id;
      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Fetch URL metadata if URL changed
      let urlMetadata = {};
      if (formData.website_url) {
        try {
          const metadata = await fetchURLMetadata(formData.website_url);
          urlMetadata = {
            url_title: metadata.title,
            url_description: metadata.description,
            url_image: metadata.image,
            url_site_name: metadata.siteName,
            url_favicon: metadata.favicon
          };
        } catch (error) {
          console.warn('Failed to fetch URL metadata:', error);
        }
      }

      // Use URL metadata for business name and description
      const businessName = (urlMetadata as any).url_title || 'Untitled Business';
      const businessDescription = (urlMetadata as any).url_description || null;

      // Update business
      const { error: businessError } = await supabase
        .from('curated_businesses')
        .update({
          business_name: businessName,
          description: businessDescription,
          website_url: formData.website_url,
          admin_notes: formData.admin_notes || null,
          featured: formData.featured,
          applies_to_all_industries: formData.applies_to_all_industries,
          ...urlMetadata
        })
        .eq('id', id);

      if (businessError) {
        throw new Error(`Failed to update business: ${businessError.message}`);
      }

      // Update categories - delete existing and insert new
      await supabase
        .from('curated_business_netzero_categories')
        .delete()
        .eq('business_id', id);

      if (formData.categories && formData.categories.length > 0) {
        const categoryInserts = formData.categories.map(subcategoryId => ({
          business_id: id,
          subcategory_id: subcategoryId
        }));

        const { error: categoryError } = await supabase
          .from('curated_business_netzero_categories')
          .insert(categoryInserts);

        if (categoryError) {
          console.error('❌ Failed to update categories:', categoryError);
        }
      }

      // Update industries - delete existing and insert new
      await supabase
        .from('curated_business_industries')
        .delete()
        .eq('business_id', id);

      if (!formData.applies_to_all_industries && formData.industries && formData.industries.length > 0) {
        const industryInserts = formData.industries.map(industryId => ({
          business_id: id,
          industry_id: industryId
        }));

        const { error: industryError } = await supabase
          .from('curated_business_industries')
          .insert(industryInserts);

        if (industryError) {
          console.error('❌ Failed to update industries:', industryError);
        }
      }

    } catch (error) {
      console.error('Error updating business:', error);
      throw error;
    }
  }

  /**
   * Delete a curated business
   */
  static async deleteBusiness(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('curated_businesses')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(`Failed to delete business: ${error.message}`);
      }
    } catch (error) {
      console.error('Error deleting business:', error);
      throw error;
    }
  }

  /**
   * Update an existing business
   */
  static async updateBusiness(id: string, formData: CuratedBusinessFormData): Promise<void> {
    try {
      // Fetch URL metadata if URL changed
      let urlMetadata = {};
      if (formData.website_url) {
        try {
          const metadata = await fetchURLMetadata(formData.website_url);
          urlMetadata = {
            url_title: metadata.title,
            url_description: metadata.description,
            url_image: metadata.image,
            url_site_name: metadata.siteName,
            url_favicon: metadata.favicon
          };
        } catch (error) {
          console.warn('Failed to fetch URL metadata:', error);
        }
      }

      const businessName = (urlMetadata as any).url_title || 'Untitled Business';
      const businessDescription = (urlMetadata as any).url_description || null;

      // Update business
      const { error: businessError } = await supabase
        .from('curated_businesses')
        .update({
          business_name: businessName,
          description: businessDescription,
          website_url: formData.website_url,
          admin_notes: formData.admin_notes || null,
          featured: formData.featured,
          applies_to_all_industries: formData.applies_to_all_industries,
          ...urlMetadata
        })
        .eq('id', id);

      if (businessError) {
        throw new Error(`Failed to update business: ${businessError.message}`);
      }

      // Update tags - delete old ones and add new ones
      await supabase
        .from('curated_business_tags')
        .delete()
        .eq('business_id', id);

      if (formData.tags && formData.tags.length > 0) {
        const tagInserts = formData.tags.map(tag => ({
          business_id: id,
          tag_id: tag.id
        }));

        const { error: tagError } = await supabase
          .from('curated_business_tags')
          .insert(tagInserts);

        if (tagError) {
          console.warn('Failed to update tags:', tagError);
        }
      }

      // Update categories - delete old ones and add new ones
      await supabase
        .from('curated_business_netzero_categories')
        .delete()
        .eq('business_id', id);

      if (formData.categories && formData.categories.length > 0) {
        const categoryInserts = formData.categories.map(subcategoryId => ({
          business_id: id,
          subcategory_id: subcategoryId
        }));

        const { error: categoryError } = await supabase
          .from('curated_business_netzero_categories')
          .insert(categoryInserts);

        if (categoryError) {
          console.warn('Failed to update categories:', categoryError);
        }
      }

      // Update industries - delete old ones and add new ones
      await supabase
        .from('curated_business_industries')
        .delete()
        .eq('business_id', id);

      if (!formData.applies_to_all_industries && formData.industries && formData.industries.length > 0) {
        const industryInserts = formData.industries.map(industryId => ({
          business_id: id,
          industry_id: industryId
        }));

        const { error: industryError } = await supabase
          .from('curated_business_industries')
          .insert(industryInserts);

        if (industryError) {
          console.warn('Failed to update industries:', industryError);
        }
      }
    } catch (error) {
      console.error('Error updating business:', error);
      throw error;
    }
  }

  /**
   * Delete a business
   */
  static async deleteBusiness(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('curated_businesses')
        .delete()
        .eq('id', id);

      if (error) {
        throw new Error(`Failed to delete business: ${error.message}`);
      }
    } catch (error) {
      console.error('Error deleting business:', error);
      throw error;
    }
  }

  /**
   * Toggle featured status
   */
  static async toggleFeatured(id: string, featured: boolean): Promise<void> {
    try {
      const { error } = await supabase
        .from('curated_businesses')
        .update({ featured })
        .eq('id', id);

      if (error) {
        throw new Error(`Failed to update featured status: ${error.message}`);
      }
    } catch (error) {
      console.error('Error toggling featured status:', error);
      throw error;
    }
  }
}