import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { useNavigate, useParams } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { NetZeroCategorySelector } from '@/components/netzero/NetZeroCategorySelector';
import { IndustrySelector } from '@/components/industries/IndustrySelector';
import TagInput from '@/components/tags/TagInput';
import URLPreview from '@/features/social/components/URLPreview';
import { CuratedBusinessService, type CuratedBusiness, type CuratedBusinessFormData } from '../services/curatedBusinessService';
import type { Tag } from '@/types/tags.types';

interface CuratedBusinessFormProps {
  business?: CuratedBusiness | null;
  mode: 'create' | 'edit';
}

const CuratedBusinessForm: React.FC<CuratedBusinessFormProps> = ({
  business,
  mode
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { id } = useParams<{ id: string }>();
  
  const [formData, setFormData] = useState<CuratedBusinessFormData>({
    website_url: '',
    featured: false,
    applies_to_all_industries: false,
    tags: [],
    categories: [],
    industries: [],
    admin_notes: ''
  });
  
  const [loading, setLoading] = useState(false);
  const [loadingBusiness, setLoadingBusiness] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [currentBusiness, setCurrentBusiness] = useState<CuratedBusiness | null>(business || null);

  // Load business data when editing
  useEffect(() => {
    if (mode === 'edit' && id && !currentBusiness) {
      const loadBusiness = async () => {
        try {
          setLoadingBusiness(true);
          const businessData = await CuratedBusinessService.getBusinessById(id);
          setCurrentBusiness(businessData);
        } catch (error) {
          console.error('Failed to load business:', error);
          toast({
            title: "Error",
            description: "Failed to load business data",
            variant: "destructive"
          });
          navigate('/keeping-an-eye-on');
        } finally {
          setLoadingBusiness(false);
        }
      };
      loadBusiness();
    }
  }, [mode, id, currentBusiness, navigate, toast]);

  // Initialize form data when editing
  useEffect(() => {
    if (mode === 'edit' && currentBusiness) {
      setFormData({
        website_url: currentBusiness.website_url,
        featured: currentBusiness.featured,
        applies_to_all_industries: currentBusiness.applies_to_all_industries,
        tags: currentBusiness.tags || [],
        categories: currentBusiness.categories?.map(cat => cat.id) || [],
        industries: currentBusiness.industries?.map(ind => ind.id) || [],
        admin_notes: currentBusiness.admin_notes || ''
      });
      setShowPreview(true);
    }
  }, [mode, currentBusiness]);

  const handleInputChange = (field: keyof CuratedBusinessFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Show preview when URL is entered
    if (field === 'website_url' && value) {
      setShowPreview(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.website_url.trim()) {
      toast({
        title: "Error",
        description: "Website URL is required",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);
      
      if (mode === 'create') {
        await CuratedBusinessService.createBusiness(formData);
        toast({
          title: "Success",
          description: "Business added successfully"
        });
      } else if (currentBusiness) {
        await CuratedBusinessService.updateBusiness(currentBusiness.id, formData);
        toast({
          title: "Success", 
          description: "Business updated successfully"
        });
      }
      
      navigate('/keeping-an-eye-on');
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save business",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  if (loadingBusiness) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading business data...</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle>
              {mode === 'create' ? 'Add Business to Directory' : 'Edit Business'}
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Add a business to the "Keeping an Eye On" directory. We'll fetch metadata from the URL automatically.
            </p>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              
              {/* Website URL */}
              <div>
                <Label htmlFor="website_url">Website URL *</Label>
                <Input
                  id="website_url"
                  type="url"
                  value={formData.website_url}
                  onChange={(e) => handleInputChange('website_url', e.target.value)}
                  placeholder="https://example.com"
                  required
                />
                <p className="text-xs text-muted-foreground mt-1">
                  We'll automatically fetch the business name, description, and preview image from this URL.
                </p>
              </div>

              {/* URL Preview */}
              {showPreview && formData.website_url && (
                <div>
                  <Label>Preview</Label>
                  <div className="border rounded-lg p-4 bg-white">
                    <URLPreview url={formData.website_url} showPreview={true} />
                  </div>
                </div>
              )}

              {/* Featured */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <Label htmlFor="featured">Featured Business</Label>
                <p className="text-xs text-muted-foreground ml-2">
                  Featured businesses appear at the top of the directory
                </p>
              </div>

              {/* All Industries Option */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="applies_to_all_industries"
                    checked={formData.applies_to_all_industries}
                    onCheckedChange={(checked) => handleInputChange('applies_to_all_industries', !!checked)}
                  />
                  <Label htmlFor="applies_to_all_industries">Applies to All Industries</Label>
                  <p className="text-xs text-muted-foreground ml-2">
                    Check this if the business is relevant to all industries
                  </p>
                </div>

                {/* Industry Selector - only show if not applies to all */}
                {!formData.applies_to_all_industries && (
                  <div>
                    <Label>Industries</Label>
                    <IndustrySelector
                      selectedIndustries={formData.industries.map(id => ({ industry_id: id }))}
                      onIndustriesChange={(industries) => handleInputChange('industries', industries.map(i => i.industry_id))}
                      mode="multi"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Select specific industries this business serves
                    </p>
                  </div>
                )}
              </div>

              {/* Net Zero Categories */}
              <div>
                <Label>Net Zero Categories</Label>
                                    <NetZeroCategorySelector
                      selectedSubcategories={formData.categories}
                      onSelectionChange={(subcategoryIds) => handleInputChange('categories', subcategoryIds)}
                      multiple={true}
                    />
                <p className="text-xs text-muted-foreground mt-1">
                  Select relevant Net Zero categories for this business
                </p>
              </div>

              {/* Tags */}
              <div>
                <Label>Tags</Label>
                <TagInput
                  selectedTags={formData.tags}
                  onTagsChange={(tags) => handleInputChange('tags', tags)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Add relevant tags to help users discover this business
                </p>
              </div>

              {/* Admin Notes */}
              <div>
                <Label htmlFor="admin_notes">Admin Notes</Label>
                <Textarea
                  id="admin_notes"
                  value={formData.admin_notes}
                  onChange={(e) => handleInputChange('admin_notes', e.target.value)}
                  placeholder="Internal notes about this business (not visible to public)"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Internal notes for admin reference only
                </p>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/keeping-an-eye-on')}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Saving...' : mode === 'create' ? 'Add Business' : 'Update Business'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CuratedBusinessForm;