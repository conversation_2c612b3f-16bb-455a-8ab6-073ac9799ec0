-- Add official_partner column to businesses table
ALTER TABLE "public"."businesses" 
ADD COLUMN "official_partner" boolean DEFAULT false NOT NULL;

-- Add comment to the column
COMMENT ON COLUMN "public"."businesses"."official_partner" IS 'Indicates if the business is an official partner of NeXzero';

-- Create index for faster filtering on official partner businesses
CREATE INDEX "idx_businesses_official_partner" ON "public"."businesses" USING "btree" ("official_partner");

-- Update the business_directory view to include official_partner
DROP VIEW IF EXISTS "public"."business_directory";

CREATE OR REPLACE VIEW "public"."business_directory" AS
 SELECT "id",
    "business_name",
    "contact_email",
    "contact_phone",
    "website",
    "linkedin",
    "twitter",
    "city",
    "postcode",
    "description",
    "is_verified",
    "official_partner",
    "carbon_reduction_plan",
    "financial_roi_calculation",
    "carbon_impact_calculation",
    "created_at",
    "updated_at"
   FROM "public"."businesses";

ALTER VIEW "public"."business_directory" OWNER TO "postgres";
