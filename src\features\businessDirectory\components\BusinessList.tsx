import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import type { Database } from '@/types/database.types';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Pencil, Trash2, Images, Package, BarChart3, Mail, Globe, Phone, MapPin, TrendingUp, Shield, Star } from 'lucide-react';
import BusinessForm from './BusinessForm';
import BusinessImageManager from './BusinessImageManager';
import BusinessLogoDisplay from './BusinessLogoDisplay';
import { cloudflareService } from '@/services/cloudflareService';

type Business = Database['public']['Tables']['businesses']['Row'];

interface BusinessListProps {
  userId: string;
  onUpgradeToSubscriptions?: () => void;
}

const BusinessList: React.FC<BusinessListProps> = ({ userId, onUpgradeToSubscriptions }) => {
  const navigate = useNavigate();
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingBusiness, setEditingBusiness] = useState<Business | null>(null);
  const [managingImages, setManagingImages] = useState<Business | null>(null);
  const { toast } = useToast();

  const fetchBusinesses = async () => {
    try {
      const { data, error } = await supabase
        .from('businesses')
        .select('*')
        .eq('owner_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      // Cast the data to include the new image fields
      setBusinesses((data || []) as Business[]);
    } catch (error) {
      console.error('Error fetching businesses:', error);
      toast({
        title: "Error",
        description: "Failed to load businesses",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (businessId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this business?');
    if (!confirmed) return;

    try {
      // First, get business data to find images that need to be deleted
      const { data: business, error: fetchError } = await supabase
        .from('businesses')
        .select('logo_cloudflare_key')
        .eq('id', businessId)
        .single();

      if (fetchError) {
        console.error('Error fetching business for deletion:', fetchError);
        throw fetchError;
      }

      // Delete logo from Cloudflare R2 if it exists
      if (business?.logo_cloudflare_key) {
        try {
          await cloudflareService.deleteBusinessImage(business.logo_cloudflare_key);
          console.log('Successfully deleted business logo from Cloudflare R2');
        } catch (logoError) {
          console.error('Error deleting business logo from Cloudflare R2:', logoError);
          // Continue with deletion even if logo cleanup fails
        }
      }

      // Delete product images from Cloudflare R2 and database
      try {
        // Get all product images for this business
        const { data: productImages, error: imagesError } = await supabase
          .from('product_images')
          .select('*')
          .eq('business_id', businessId);

        if (imagesError) {
          console.error('Error fetching product images for business deletion:', imagesError);
        } else if (productImages && productImages.length > 0) {
          // Delete each image from Cloudflare R2
          for (const image of productImages) {
            if (image.cloudflare_key) {
              try {
                await cloudflareService.deleteBusinessImage(image.cloudflare_key);
                console.log(`Successfully deleted product image ${image.cloudflare_key} from Cloudflare R2`);
              } catch (imageError) {
                console.error(`Error deleting product image ${image.cloudflare_key}:`, imageError);
                // Continue with other images even if one fails
              }
            }
          }

          // Delete all product image records from database
          const { error: deleteImagesError } = await supabase
            .from('product_images')
            .delete()
            .eq('business_id', businessId);

          if (deleteImagesError) {
            console.error('Error deleting product images from database:', deleteImagesError);
          } else {
            console.log(`Successfully deleted ${productImages.length} product image records from database`);
          }
        }
      } catch (productImageError) {
        console.error('Error during product image cleanup:', productImageError);
        // Continue with business deletion even if product image cleanup fails
      }

      // Delete business from database
      const { error } = await supabase
        .from('businesses')
        .delete()
        .eq('id', businessId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Business and all associated images deleted successfully"
      });

      // Refresh the list
      fetchBusinesses();
    } catch (error) {
      console.error('Error deleting business:', error);
      toast({
        title: "Error",
        description: "Failed to delete business",
        variant: "destructive"
      });
    }
  };

  useEffect(() => {
    fetchBusinesses();
  }, [userId]);

  if (loading) {
    return <div>Loading businesses...</div>;
  }

  if (businesses.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-muted-foreground text-center">You haven't added any businesses yet.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <div className="px-3 sm:px-0 space-y-4"> {/* Add mobile padding to match container standard */}
        <h3 className="text-lg font-semibold mb-4">Your Businesses</h3>
        {businesses.map((business) => (
          <Card
            key={business.id}
            className="group cursor-pointer hover:shadow-lg transition-shadow overflow-hidden" // Add overflow-hidden
            onClick={() => navigate(`/business/${business.id}`)}
          >
            <CardHeader className="pb-4">
              {/* Business name at the top */}
              <div className="flex items-center justify-between mb-4 gap-4 overflow-hidden"> {/* Add gap and overflow-hidden */}
                <CardTitle className="text-xl sm:text-2xl truncate min-w-0 flex-1">{business.business_name}</CardTitle> {/* Make responsive and add truncate */}
                <div className="flex items-center gap-2 flex-shrink-0">
                  {business.is_verified && (
                    <Badge variant="default" className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 flex items-center gap-1">
                      <Shield className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">Verified</span>
                    </Badge>
                  )}
                  {(business as any).official_partner && (
                    <Badge variant="default" className="bg-yellow-600 hover:bg-yellow-700 text-white text-xs px-2 py-1 flex items-center gap-1">
                      <Star className="w-3 h-3 flex-shrink-0" />
                      <span className="truncate">Official Partner</span>
                    </Badge>
                  )}
                </div>
                {/* Action buttons moved to top right */}
                <div
                  className="flex gap-1 flex-shrink-0" // Add flex-shrink-0
                  onClick={(e) => e.stopPropagation()}>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(`/business/${business.id}/products?tab=products`)}
                    title="Manage Products & Services"
                    className="h-8 w-8 p-0"
                  >
                    <Package className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(`/business/${business.id}/products?tab=metrics`)}
                    title="Manage Business Metrics"
                    className="h-8 w-8 p-0"
                  >
                    <TrendingUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      if (e.ctrlKey || e.metaKey) {
                        // Ctrl/Cmd+Click opens in new tab
                        window.open(`/business/${business.id}/images`, '_blank');
                      } else if (e.shiftKey) {
                        // Shift+Click navigates to dedicated page
                        navigate(`/business/${business.id}/images`);
                      } else {
                        // Normal click opens modal
                        setManagingImages(business as any);
                      }
                    }}
                    title="Manage Images (Shift+Click for full page, Ctrl+Click for new tab)"
                    className="h-8 w-8 p-0"
                  >
                    <Images className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setEditingBusiness(business)}
                    title="Edit Business"
                    className="h-8 w-8 p-0"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(business.id)}
                    title="Delete Business"
                    className="h-8 w-8 p-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Visual divider */}
              <div className="w-full h-px bg-border mb-6"></div>

              {/* Main content area with logo and details */}
              <div className="flex items-start gap-3 sm:gap-4 md:gap-6 overflow-hidden"> {/* Adjust gaps responsively and add overflow-hidden */}
                {/* Business details on the left */}
                <div className="flex-1 min-w-0 space-y-2 sm:space-y-3 overflow-hidden"> {/* Add overflow-hidden */}
                  {business.contact_email && (
                    <div className="flex items-center gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                      <Mail className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <CardDescription className="text-base truncate min-w-0">{business.contact_email}</CardDescription> {/* Add truncate */}
                    </div>
                  )}

                  {business.website && (
                    <div className="flex items-center gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                      <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <a
                        href={business.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline text-base truncate min-w-0 flex-1" // Add truncate and flex classes
                        onClick={(e) => e.stopPropagation()}
                      >
                        {business.website.replace(/^https?:\/\//, '')}
                      </a>
                    </div>
                  )}

                  {business.contact_phone && (
                    <div className="flex items-center gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                      <Phone className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-base truncate min-w-0">{business.contact_phone}</span> {/* Add truncate */}
                    </div>
                  )}

                  {(business.city || business.postcode) && (
                    <div className="flex items-center gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                      <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="text-base truncate min-w-0"> {/* Add truncate */}
                        {[business.city, business.postcode].filter(Boolean).join(', ')}
                      </span>
                    </div>
                  )}
                </div>

                {/* Larger business logo on the right */}
                <div className="flex-shrink-0">
                  <BusinessLogoDisplay
                    logoUrl={business.logo_url}
                    businessName={business.business_name}
                    size="xl"
                    shape="square"
                    className="w-[60px] h-[60px] sm:w-[80px] sm:h-[80px] md:w-[100px] md:h-[100px] lg:w-[120px] lg:h-[120px]" // Make logo more responsive
                  />
                </div>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>

      <AlertDialog open={!!editingBusiness} onOpenChange={(open) => !open && setEditingBusiness(null)}>
        <AlertDialogContent className="max-w-4xl h-[85vh] p-6 gap-0 flex flex-col">
          <AlertDialogHeader className="flex-none">
            <AlertDialogTitle>Edit Business</AlertDialogTitle>
            <AlertDialogDescription>
              Make changes to your business information below.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="flex-1 overflow-y-auto -mx-6 px-6">
            {editingBusiness && (
              <BusinessForm
                initialData={editingBusiness as any}
                onSubmit={() => {
                  setEditingBusiness(null);
                  fetchBusinesses();
                }}
                onCancel={() => setEditingBusiness(null)}
                onUpgradeToSubscriptions={onUpgradeToSubscriptions}
              />
            )}
          </div>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={!!managingImages} onOpenChange={(open) => {
        if (!open) {
          setManagingImages(null);
          // Refetch businesses when modal closes to get final state
          fetchBusinesses();
        }
      }}>
        <AlertDialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <AlertDialogHeader>
            <AlertDialogTitle>Manage Business Images</AlertDialogTitle>
            <AlertDialogDescription>
              Upload and manage your business logo and product images.
            </AlertDialogDescription>
          </AlertDialogHeader>
          {managingImages && (
            <BusinessImageManager
              business={managingImages as any}
              onImagesUpdated={() => {
                // Don't refetch immediately - let the BusinessImageManager handle its own state
                // Only refetch when the modal is closed to get final state
              }}
            />
          )}
          <AlertDialogFooter>
            <Button onClick={() => setManagingImages(null)}>
              Done
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default BusinessList;
