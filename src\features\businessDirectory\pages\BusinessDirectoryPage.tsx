import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { CategoryButton } from "@/components/ui/category-button";
import { MapPin, Globe, Calendar, Building2, Search, X, Shield, Filter, ChevronDown, ChevronRight, ArrowUpDown, Leaf, Calculator, TrendingUp, CheckCircle, Star } from "lucide-react";
import { supabase } from '@/integrations/supabase/client';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import { getNetZeroCategoryTheme, getNetZeroCategoryThemeById, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import BusinessLogoDisplay from '../components/BusinessLogoDisplay';
import { CategoryCardsDemoStyle } from '@/components/shared/CategoryCardsDemoStyle';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { UKIndustryService } from '@/services/ukIndustryService';
import BusinessLocationFilter from '../components/BusinessLocationFilter';
import BusinessFilters from '../components/BusinessFilters';
import type { Database } from '@/types/database.types';
import type { NetZeroSubcategoryWithCategory, NetZeroCategoryWithSubcategories } from '@/types/netzero-categories.types';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';
import type { BusinessFilterState } from '../components/BusinessFilters';

type Business = Database['public']['Tables']['businesses']['Row'];
type VerificationStatus = 'yes_verified' | 'yes_self' | 'no' | 'in_progress' | 'na';

// Helper function to get verification status display
const getVerificationStatusDisplay = (status: VerificationStatus | null | undefined) => {
  if (!status) return { label: 'Not specified', icon: X, color: 'text-gray-500', bgColor: 'bg-gray-100' };

  switch (status) {
    case 'yes_verified':
      return { label: 'Yes (independently verified)', icon: Shield, color: 'text-green-700', bgColor: 'bg-green-100' };
    case 'yes_self':
      return { label: 'Yes (self verified)', icon: Shield, color: 'text-blue-700', bgColor: 'bg-blue-100' };
    case 'no':
      return { label: 'No', icon: X, color: 'text-red-700', bgColor: 'bg-red-100' };
    case 'in_progress':
      return { label: 'In progress', icon: Calendar, color: 'text-yellow-700', bgColor: 'bg-yellow-100' };
    case 'na':
      return { label: 'Not applicable', icon: Calendar, color: 'text-gray-700', bgColor: 'bg-gray-100' };
    default:
      return { label: 'Not specified', icon: X, color: 'text-gray-500', bgColor: 'bg-gray-100' };
  }
};

// Extended business type with location info
interface BusinessWithLocation extends Business {
  headquarters_location?: {
    id: string;
    name: string;
    slug: string;
    path: string;
  } | null;
  customer_locations?: Array<{
    id: string;
    name: string;
    slug: string;
    type: string;
    path: string;
  }> | null;
}

// Sort options for businesses
export type BusinessSortOption = 'name-asc' | 'name-desc' | 'joined-asc' | 'joined-desc';

// Filter state interface - now using BusinessFilterState from BusinessFilters component
interface FilterState extends BusinessFilterState {
  selectedCategoryId: string; // For category card filtering
  expandedIndustryCategories: Set<string>;
  expandedNetZeroCategories: Set<string>;
}

const BusinessDirectoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [businesses, setBusinesses] = useState<any[]>([]); // Using any for now to bypass TypeScript issues
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [industries, setIndustries] = useState<UKIndustryWithChildren[]>([]);
  const [loadingIndustries, setLoadingIndustries] = useState(true);
  const [netZeroCategories, setNetZeroCategories] = useState<NetZeroCategoryWithSubcategories[]>([]);
  const [loadingNetZeroCategories, setLoadingNetZeroCategories] = useState(true);
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    showFilters: false,
    searchTerm: '',
    verifiedOnly: false,
    officialPartnerOnly: false,
    selectedLocations: [],
    selectedIndustries: [],
    selectedNetZeroCategories: [],
    sortBy: 'joined-desc',
    carbonReductionPlan: false,
    financialRoiCalculation: false,
    carbonImpactCalculation: false,
    selectedCategoryId: '',
    expandedIndustryCategories: new Set(),
    expandedNetZeroCategories: new Set()
  });

  // Load industries and net-zero categories for filtering
  useEffect(() => {
    const loadIndustries = async () => {
      try {
        setLoadingIndustries(true);
        const industriesData = await UKIndustryService.getAllIndustriesWithChildren();
        setIndustries(industriesData);
      } catch (err) {
        console.error('Failed to load industries:', err);
      } finally {
        setLoadingIndustries(false);
      }
    };

    const loadNetZeroCategories = async () => {
      try {
        setLoadingNetZeroCategories(true);
        const categoriesData = await NetZeroCategoryService.getAllCategoriesWithSubcategories();
        setNetZeroCategories(categoriesData);
      } catch (err) {
        console.error('Failed to load net-zero categories:', err);
      } finally {
        setLoadingNetZeroCategories(false);
      }
    };

    loadIndustries();
    loadNetZeroCategories();
  }, []);

  useEffect(() => {
    const fetchBusinessesWithLocations = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all businesses with their full details - using any to bypass TypeScript issues
        const { data: businessData, error: businessError } = await supabase
          .from('businesses' as any)
          .select('*')
          .order('created_at', { ascending: false });

        if (businessError) throw businessError;

        console.log('Business data sample:', businessData?.[0]); // Debug log

        // For each business, try to get headquarters location details manually
        const businessesWithLocations = await Promise.all(
          (businessData || []).map(async (business: any) => {
            let headquartersLocation = null;
            if (business.headquarters_location_id) {
              try {
                // Get headquarters location details - using any to bypass TypeScript issues
                const { data: hqLocationData, error: hqLocationError } = await supabase
                  .from('locations' as any)
                  .select('id, name, slug, type')
                  .eq('id', business.headquarters_location_id)
                  .single();

                if (!hqLocationError && hqLocationData) {
                  console.log('HQ Location data for', business.business_name, ':', hqLocationData); // Debug log
                  const locationInfo = hqLocationData as any; // Cast to any to bypass TypeScript issues
                  headquartersLocation = {
                    headquarters_name: locationInfo.name,
                    headquarters_slug: locationInfo.slug,
                    headquarters_id: locationInfo.id,
                    headquarters_path: null // We'll calculate this later if needed
                  };
                }
              } catch (err) {
                console.warn('Could not fetch HQ location for business:', business.business_name, err);
              }
            }

            // Fetch net-zero categories for each business
            let netZeroCategories = [];
            let primaryNetZeroCategory = undefined;
            try {
              const { categories, primary_category } = await NetZeroCategoryService.getBusinessCategories(business.id);
              netZeroCategories = categories;
              primaryNetZeroCategory = primary_category;
            } catch (err) {
              console.warn('Could not fetch net-zero categories for business:', business.business_name, err);
            }

            // Fetch main industry and target industries with better error handling
            let mainIndustry = null;
            let targetIndustries = [];
            
            try {
              // Fetch main industry if business has main_industry_id
              if (business.main_industry_id) {
                try {
                  const { data: mainIndustryData, error: mainIndustryError } = await supabase
                    .from('uk_industries')
                    .select('*')
                    .eq('id', business.main_industry_id)
                    .maybeSingle();

                  if (!mainIndustryError && mainIndustryData) {
                    // Fetch parent separately if it exists
                    let parent = null;
                    if (mainIndustryData.parent_id) {
                      const { data: parentData, error: parentError } = await supabase
                        .from('uk_industries')
                        .select('*')
                        .eq('id', mainIndustryData.parent_id)
                        .maybeSingle();
                      
                      if (!parentError && parentData) {
                        parent = parentData;
                      }
                    }
                    
                    mainIndustry = {
                      ...mainIndustryData,
                      parent
                    };
                  } else if (mainIndustryError) {
                    console.error('Error fetching main industry:', mainIndustryError);
                  }
                } catch (mainIndustryErr) {
                  console.error('Exception fetching main industry for business:', business.business_name, mainIndustryErr);
                }
              }

              // Fetch target industries
              try {
                const { data: targetIndustriesData, error: targetError } = await supabase
                  .from('business_target_industries')
                  .select('industry_id')
                  .eq('business_id', business.id);

                if (!targetError && targetIndustriesData && targetIndustriesData.length > 0) {
                  // Get the industry IDs
                  const industryIds = targetIndustriesData.map(item => item.industry_id);
                  
                  // Fetch the industry details with parent data
                  const { data: industriesData, error: industriesError } = await supabase
                    .from('uk_industries')
                    .select('*')
                    .in('id', industryIds);

                  if (!industriesError && industriesData) {
                    // Fetch parents for each industry separately
                    targetIndustries = await Promise.all(
                      industriesData.map(async (industry) => {
                        let parent = null;
                        if (industry.parent_id) {
                          const { data: parentData, error: parentError } = await supabase
                            .from('uk_industries')
                            .select('*')
                            .eq('id', industry.parent_id)
                            .maybeSingle();
                          
                          if (!parentError && parentData) {
                            parent = parentData;
                          }
                        }
                        
                        return {
                          ...industry,
                          parent
                        };
                      })
                    );
                  } else if (industriesError) {
                    console.error('Error fetching target industries details:', industriesError);
                  }
                } else if (targetError) {
                  console.error('Error fetching target industries:', targetError);
                }
              } catch (targetIndustriesErr) {
                console.error('Exception fetching target industries for business:', business.business_name, targetIndustriesErr);
              }
            } catch (err) {
              console.error('Exception in industry fetching for business:', business.business_name, err);
            }



            return {
              ...business,
              headquarters_location: headquartersLocation,
              netZeroCategories,
              primaryNetZeroCategory,
              mainIndustry,
              targetIndustries
            };
          })
        );


        
        setBusinesses(businessesWithLocations);
      } catch (err) {
        console.error('Failed to fetch businesses:', err);
        setError(err instanceof Error ? err.message : 'Failed to load businesses');
      } finally {
        setLoading(false);
      }
    };

    fetchBusinessesWithLocations();
  }, []);



  // Filter and sort businesses based on current filters and sort option
  const filteredAndSortedBusinesses = useMemo(() => {
    // First filter
    const filtered = businesses.filter((business) => {
      // Search filter
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const matches = business.business_name?.toLowerCase().includes(searchLower) ||
          business.company_description?.toLowerCase().includes(searchLower) ||
          business.website?.toLowerCase().includes(searchLower) ||
          (business.headquarters_location?.headquarters_name?.toLowerCase().includes(searchLower)) ||
          
          // Search in main industry
          (business.mainIndustry?.name?.toLowerCase().includes(searchLower)) ||
          (business.mainIndustry?.parent?.name?.toLowerCase().includes(searchLower)) ||
          
          // Search in target industries
          (business.targetIndustries?.some((industry: any) =>
            industry.name?.toLowerCase().includes(searchLower) ||
            industry.parent?.name?.toLowerCase().includes(searchLower)
          )) ||
          
          // Search in net-zero categories
          (business.primaryNetZeroCategory?.name?.toLowerCase().includes(searchLower)) ||
          (business.netZeroCategories?.some((category: any) =>
            category.name?.toLowerCase().includes(searchLower)
          ));
        
        if (!matches) {
          return false;
        }
      }

      // Verified filter
      if (filters.verifiedOnly && !business.is_verified) {
        return false;
      }

      // Official Partner filter
      if (filters.officialPartnerOnly && !business.official_partner) {
        return false;
      }

      // Industries filter (combines main and target industries)
      if (filters.selectedIndustries.length > 0) {
        const hasMatchingIndustry = 
          // Check main industry
          (business.mainIndustry && filters.selectedIndustries.includes(business.mainIndustry.id)) ||
          // Check target industries
          business.targetIndustries?.some((industry: any) =>
            filters.selectedIndustries.includes(industry.id)
          );
        if (!hasMatchingIndustry) {
          return false;
        }
      }

      // Net-zero categories filter
      if (filters.selectedNetZeroCategories.length > 0) {
        const hasMatchingNetZeroCategory = business.netZeroCategories?.some((category: any) =>
          filters.selectedNetZeroCategories.includes(category.id)
        );
        if (!hasMatchingNetZeroCategory) {
          return false;
        }
      }

      // Category card filter (main Net Zero category)
      if (filters.selectedCategoryId) {
        if (filters.selectedCategoryId === 'not-classified') {
          // Show businesses with no Net Zero categories
          const hasNoCategories = !business.netZeroCategories || business.netZeroCategories.length === 0;
          if (!hasNoCategories) {
            return false;
          }
        } else {
          // Show businesses with any subcategory whose parent category slug matches the selected card id
          const slugify = (str: string) => str.toLowerCase().replace(/\s+/g, '-');
          const hasMatchingParentCategory = business.netZeroCategories?.some((subcategory: any) =>
            subcategory.category && slugify(subcategory.category.name) === filters.selectedCategoryId
          );
          if (!hasMatchingParentCategory) {
            return false;
          }
        }
      }

      // Sustainability filters - only show businesses with verified status
      if (filters.carbonReductionPlan) {
        const status = (business as any).carbon_reduction_plan;
        if (status !== 'yes_verified' && status !== 'yes_self') {
          return false;
        }
      }

      if (filters.financialRoiCalculation) {
        const status = (business as any).financial_roi_calculation;
        if (status !== 'yes_verified' && status !== 'yes_self') {
          return false;
        }
      }

      if (filters.carbonImpactCalculation) {
        const status = (business as any).carbon_impact_calculation;
        if (status !== 'yes_verified' && status !== 'yes_self') {
          return false;
        }
      }

      // Location filter
      if (filters.selectedLocations.length > 0) {
        const businessLocationId = business.headquarters_location_id;
        if (!businessLocationId || !filters.selectedLocations.includes(businessLocationId)) {
          return false;
        }
      }

      return true;
    });

    // Then sort
    return filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'name-asc':
          return (a.business_name || '').localeCompare(b.business_name || '');
        case 'name-desc':
          return (b.business_name || '').localeCompare(a.business_name || '');
        case 'joined-asc':
          return new Date(a.created_at || 0).getTime() - new Date(b.created_at || 0).getTime();
        case 'joined-desc':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        default:
          return 0;
      }
    });
  }, [businesses, filters]);



  // Filter helper functions - now handled by BusinessFilters component
  const handleCategorySelect = (categoryId: string, categoryName: string) => {
    setFilters(prev => ({
      ...prev,
      selectedCategoryId: prev.selectedCategoryId === categoryId ? '' : categoryId
    }));
  };

  const clearAllFilters = () => {
    setFilters(prev => ({
      ...prev,
      showFilters: false,
      searchTerm: '',
      verifiedOnly: false,
      selectedLocations: [],
      selectedIndustries: [],
      selectedNetZeroCategories: [],
      sortBy: 'joined-desc',
      carbonReductionPlan: false,
      financialRoiCalculation: false,
      carbonImpactCalculation: false,
      selectedCategoryId: '',
      expandedIndustryCategories: new Set(),
      expandedNetZeroCategories: new Set()
    }));
  };

  const hasActiveFilters = () => {
    return filters.searchTerm || 
           filters.verifiedOnly || 
           filters.selectedLocations.length > 0 || 
           filters.selectedIndustries.length > 0 ||
           filters.selectedNetZeroCategories.length > 0 ||
           filters.carbonReductionPlan ||
           filters.financialRoiCalculation ||
           filters.carbonImpactCalculation;
  };

  // Filter rendering functions - now handled by BusinessFilters component
  // Removed: renderIndustryFilter and renderNetZeroCategoriesFilter

  // Dynamically generate Net Zero categories and counts from filteredAndSortedBusinesses
  const netZeroCategoryMap = new Map();
  let notClassifiedCount = 0;
  filteredAndSortedBusinesses.forEach(business => {
    if (business.netZeroCategories && business.netZeroCategories.length > 0) {
      // Get unique parent categories for this business
      const parentCategories = new Set();
      business.netZeroCategories.forEach(subcat => {
        const catName = subcat.category?.name || 'Unknown';
        parentCategories.add(catName);
      });
      
      // Count this business once for each parent category it belongs to
      parentCategories.forEach(catName => {
        if (!netZeroCategoryMap.has(catName)) {
          netZeroCategoryMap.set(catName, {
            id: String(catName).toLowerCase().replace(/\s+/g, '-'),
            name: catName,
            description: business.netZeroCategories.find(subcat => subcat.category?.name === catName)?.category?.description || '',
            count: 0
          });
        }
        netZeroCategoryMap.get(catName).count++;
      });
    } else {
      notClassifiedCount++;
    }
  });
  const netZeroCategoriesDynamic = Array.from(netZeroCategoryMap.values());
  if (notClassifiedCount > 0) {
    netZeroCategoriesDynamic.push({
      id: 'not-classified',
      name: 'Not Classified',
      description: "Businesses that haven't been assigned to any Net Zero category yet",
      count: notClassifiedCount
    });
  }

  // Show sign-in prompt for unauthenticated users - REMOVED TO ALLOW GUEST ACCESS
  // Guest users can now browse businesses with limited information

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Business Directory</h1>
        <p className="text-muted-foreground">
          Browse sustainable businesses in our network with their headquarters locations.
        </p>
        
        {/* Guest user notice */}
        {!user && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Shield className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800">
                  Browsing as guest - Limited information shown
                </p>
                <p className="text-sm text-blue-700 mt-1">
                  <Link to="/auth?mode=signup" className="font-medium underline hover:no-underline">
                    Sign up for free
                  </Link>{" "}
                  to view contact details, websites, and connect with businesses.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Net Zero Category Cards */}
      <CategoryCardsDemoStyle
        categories={netZeroCategoriesDynamic}
        type="netzero"
        title="Net Zero Categories"
        size="md"
        selectedCategoryId={filters.selectedCategoryId}
        onCategorySelect={handleCategorySelect}
      />

      {/* Business Filters Component */}
      <BusinessFilters
        filters={filters}
        onFiltersChange={(newFilters) => setFilters(prev => ({ ...prev, ...newFilters }))}
        totalBusinesses={businesses.length}
        filteredCount={filteredAndSortedBusinesses.length}
      />
      
      {loading && (
        <div className="text-center py-8">
          <p>Loading businesses...</p>
        </div>
      )}

      {error && (
        <div className="p-4 border border-red-200 bg-red-50 text-red-700 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      {!loading && !error && filteredAndSortedBusinesses.length === 0 && (
        <div className="p-8 border border-dashed rounded-lg text-center">
          <Search className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
          <p className="text-lg">
            {hasActiveFilters() ? 'No businesses match your filters' : 'No businesses found'}
          </p>
          <p className="text-muted-foreground mt-2">
            {hasActiveFilters() ? 'Try adjusting your search terms or filters.' : 'Be the first to add your sustainable business to our directory.'}
          </p>
        </div>
      )}

      {!loading && !error && filteredAndSortedBusinesses.length > 0 && (
        <>
          <div className="mb-4">
            <p className="text-sm text-muted-foreground">
              {hasActiveFilters() 
                ? `Showing ${filteredAndSortedBusinesses.length} of ${businesses.length} businesses`
                : `${businesses.length} businesses`
              }
            </p>
          </div>
          
          <div className="px-3 sm:px-0 mb-4"> {/* Add mobile padding to match container standard */}
            <div className="grid gap-8 grid-cols-1 lg:grid-cols-2 business-directory-grid">
              {filteredAndSortedBusinesses.map((business) => {
              // Get the theme based on the business's main industry
              const industryTheme = business.mainIndustry?.parent
                ? getIndustryTheme(business.mainIndustry.parent.name)
                : getIndustryTheme('Other');

              return (
                <Card
                  key={business.id}
                  className="cursor-pointer hover:shadow-lg transition-shadow h-full overflow-hidden" // Add overflow-hidden
                  style={{
                    borderTop: `8px solid ${industryTheme.topBorderStyle?.borderTopColor ?? '#6B7280'}`,
                    borderLeft: 'none',
                    borderRight: 'none',
                    borderBottom: 'none',
                  }}
                  onClick={() => navigate(`/business/${business.id}`)}
                >
                <CardHeader className="pb-6 px-6 pt-6 overflow-hidden"> {/* Add overflow-hidden */}
                  {/* Business name at the top */}
                  <div className="flex items-center justify-between mb-2 gap-3 overflow-hidden"> {/* Add gap and overflow-hidden */}
                    <CardTitle className="text-lg leading-tight truncate min-w-0 flex-1"> {/* Add truncate and flex classes */}
                      {business.business_name}
                    </CardTitle>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      {business.is_verified && (
                        <Badge variant="default" className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-2 py-1 flex items-center gap-1">
                          <Shield className="w-3 h-3 flex-shrink-0" />
                          <span className="truncate">Verified</span>
                        </Badge>
                      )}
                      {business.official_partner && (
                        <Badge variant="default" className="bg-yellow-600 hover:bg-yellow-700 text-white text-xs px-2 py-1 flex items-center gap-1">
                          <Star className="w-3 h-3 flex-shrink-0" />
                          <span className="truncate">Official Partner</span>
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  {/* Visual divider after business name */}
                  <div className="w-full h-px bg-border mb-6"></div>
                  
                  {/* Content area with image on the right */}
                  <div className="flex items-start gap-2 sm:gap-3 md:gap-4 lg:gap-6 overflow-hidden"> {/* Make gaps responsive and add overflow-hidden */}
                    <div className="flex-1 min-w-0 space-y-3 overflow-hidden"> {/* Add overflow-hidden */}
                      {/* Headquarters Location - always show with icon for consistent alignment */}
                      <div className="flex items-start gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                        <Building2 className="w-4 h-4 text-emerald-700 mt-0.5 flex-shrink-0" />
                        <div className="min-w-0 overflow-hidden"> {/* Add overflow-hidden */}
                          <div className="text-sm font-medium">Headquarters</div>
                          {(business as any).headquarters_location?.headquarters_name ? (
                            <div className="text-sm font-medium text-emerald-700 truncate">
                              {(business as any).headquarters_location.headquarters_name}
                            </div>
                          ) : business.headquarters_location_id ? (
                            <div className="text-xs text-muted-foreground font-mono truncate">
                              ID: {business.headquarters_location_id}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground italic">
                              Not specified
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Website - Hide for non-authenticated users */}
                      <div className="flex items-center gap-2 text-sm overflow-hidden"> {/* Add overflow-hidden */}
                        <Globe className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                        <div className="min-w-0 flex-1 overflow-hidden"> {/* Add overflow-hidden */}
                          {user ? (
                            business.website ? (
                              <a 
                                href={business.website} 
                                target="_blank" 
                                rel="noopener noreferrer" 
                                className="text-primary hover:underline truncate block"
                                onClick={(e) => e.stopPropagation()}
                              >
                                {business.website.replace(/^https?:\/\//, '')}
                              </a>
                            ) : (
                              <span className="text-muted-foreground italic">No website</span>
                            )
                          ) : (
                            <div className="flex items-center gap-2">
                              <span className="text-muted-foreground italic">Website available</span>
                              <Link 
                                to="/auth?mode=signup" 
                                className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded hover:bg-blue-200 transition-colors"
                                onClick={(e) => e.stopPropagation()}
                              >
                                Sign up to view
                              </Link>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Business Description */}
                      {business.description && (
                        <div className="mt-3 overflow-hidden"> {/* Add overflow-hidden */}
                          <div className="text-sm font-medium mb-1">Description:</div>
                          <div className="text-sm leading-relaxed text-gray-600 line-clamp-4 break-words"> {/* Add break-words */}
                            {business.description.length > 350 
                              ? `${business.description.substring(0, 350).trim()}...`
                              : business.description
                            }
                          </div>
                        </div>
                      )}

                      {/* Main Industry */}
                      {business.mainIndustry && (
                        <div className="mt-2 overflow-hidden"> {/* Add overflow-hidden */}
                          <span className="text-sm text-muted-foreground mb-1 block">Main Industry:</span>
                          <div className="grid grid-cols-1 gap-1 overflow-hidden"> {/* Add overflow-hidden */}
                            <CategoryButton
                              categoryId={business.mainIndustry.id}
                              categoryName={business.mainIndustry.name}
                              theme={business.mainIndustry.parent ? getIndustryTheme(business.mainIndustry.parent.name) : getIndustryTheme('Other')}
                              variant="dynamic-button"
                              size="xs"
                              showCount={false}
                              className="w-full max-w-full min-w-0 text-xs overflow-hidden truncate" // Add overflow constraints
                            />
                          </div>
                        </div>
                      )}

                      {/* Net-Zero Areas of Interest (Parent Categories Only) */}
                      {business.netZeroCategories && business.netZeroCategories.length > 0 && (
                        <div className="mt-3 overflow-hidden"> {/* Add overflow-hidden */}
                          <div className="text-sm font-medium mb-2">Net Zero Area of Focus:</div>
                          <div className="flex flex-wrap gap-1 overflow-hidden"> {/* Add overflow-hidden */}
                            {(() => {
                              // Get unique parent categories
                              const parentCategories = new Set<string>();
                              business.netZeroCategories.forEach(subcat => {
                                if (subcat.category?.name) {
                                  parentCategories.add(subcat.category.name);
                                }
                              });
                              
                              return Array.from(parentCategories).slice(0, 3).map((parentName, index) => {
                                const theme = getNetZeroCategoryTheme(parentName);
                                return (
                                  <div key={index} className="overflow-hidden min-w-0"> {/* Add wrapper with overflow-hidden */}
                                    <CategoryButton
                                      categoryId={`netzero-${parentName.toLowerCase().replace(/\s+/g, '-')}`}
                                      categoryName={parentName}
                                      theme={theme}
                                      variant="badge"
                                      size="xs"
                                      showCount={false}
                                      className="max-w-full min-w-0 text-xs overflow-hidden truncate" // Add overflow constraints
                                    />
                                  </div>
                                );
                              });
                            })()}
                            {(() => {
                              const parentCategories = new Set<string>();
                              business.netZeroCategories.forEach(subcat => {
                                if (subcat.category?.name) {
                                  parentCategories.add(subcat.category.name);
                                }
                              });
                              return parentCategories.size > 3 ? (
                                <Badge variant="outline" className="text-xs text-muted-foreground truncate">
                                  +{parentCategories.size - 3} more
                                </Badge>
                              ) : null;
                            })()}
                          </div>
                        </div>
                      )}

                      {/* Verification Badges */}
                      {(() => {
                        // Only show badges for verified statuses
                        const carbonReductionPlan = (business as any).carbon_reduction_plan;
                        const financialRoiCalculation = (business as any).financial_roi_calculation;
                        const carbonImpactCalculation = (business as any).carbon_impact_calculation;
                        
                        const hasVerifiedStatus = [carbonReductionPlan, financialRoiCalculation, carbonImpactCalculation].some(
                          status => status === 'yes_verified' || status === 'yes_self'
                        );
                        
                        return hasVerifiedStatus;
                      })() && (
                        <div className="mt-3 overflow-hidden"> {/* Add overflow-hidden */}
                          <div className="text-sm font-medium mb-2">Sustainability Verification:</div>
                          <div className="flex flex-wrap gap-2 overflow-hidden"> {/* Add overflow-hidden */}
                            {(() => {
                              const status = (business as any).carbon_reduction_plan;
                              const isVerified = status === 'yes_verified' || status === 'yes_self';
                              
                              return isVerified ? (() => {
                                const statusDisplay = getVerificationStatusDisplay(status);
                                const Icon = statusDisplay.icon;
                                return (
                                  <Badge variant="outline" className={`text-xs ${statusDisplay.bgColor} ${statusDisplay.color} border-current truncate`}> {/* Add truncate */}
                                    <Icon className="w-3 h-3 mr-1 flex-shrink-0" />
                                    <span className="truncate">Carbon Plan</span>
                                  </Badge>
                                );
                              })() : null;
                            })()}
                            
                            {(() => {
                              const status = (business as any).financial_roi_calculation;
                              const isVerified = status === 'yes_verified' || status === 'yes_self';
                              
                              return isVerified ? (() => {
                                const statusDisplay = getVerificationStatusDisplay(status);
                                const Icon = statusDisplay.icon;
                                return (
                                  <Badge variant="outline" className={`text-xs ${statusDisplay.bgColor} ${statusDisplay.color} border-current truncate`}> {/* Add truncate */}
                                    <Icon className="w-3 h-3 mr-1 flex-shrink-0" />
                                    <span className="truncate">ROI Calc</span>
                                  </Badge>
                                );
                              })() : null;
                            })()}
                            
                            {(() => {
                              const status = (business as any).carbon_impact_calculation;
                              const isVerified = status === 'yes_verified' || status === 'yes_self';
                              
                              return isVerified ? (() => {
                                const statusDisplay = getVerificationStatusDisplay(status);
                                const Icon = statusDisplay.icon;
                                return (
                                  <Badge variant="outline" className={`text-xs ${statusDisplay.bgColor} ${statusDisplay.color} border-current truncate`}> {/* Add truncate */}
                                    <Icon className="w-3 h-3 mr-1 flex-shrink-0" />
                                    <span className="truncate">Carbon Impact</span>
                                  </Badge>
                                );
                              })() : null;
                            })()}
                          </div>
                        </div>
                      )}
                      
                      {/* Sign up prompt for guests */}
                      {!user && (
                        <div className="mt-3 p-3 bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg">
                          <div className="flex items-center gap-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-600 flex-shrink-0" />
                            <span className="text-gray-700">
                              <Link 
                                to="/auth?mode=signup" 
                                className="font-medium text-blue-700 hover:underline"
                                onClick={(e) => e.stopPropagation()}
                              >
                                Sign up for free
                              </Link>{" "}
                              to view contact details and connect directly
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    {/* Larger business image on the right */}
                    <div className="flex-shrink-0">
                      <BusinessLogoDisplay
                        logoUrl={business.logo_url}
                        businessName={business.business_name}
                        size="xl"
                        shape="square"
                        className="w-[60px] h-[60px] sm:w-[100px] sm:h-[100px] md:w-[160px] md:h-[160px] lg:w-[200px] lg:h-[200px] xl:w-[220px] xl:h-[220px]" // Make responsive starting at 60px
                      />
                    </div>
                  </div>
                </CardHeader>
                
                {/* Visual divider before date */}
                <div className="px-6">
                  <div className="w-full h-px bg-border"></div>
                </div>
                
                <CardContent className="pt-4 pb-6 px-6">
                  <div className="flex items-center gap-1.5 text-muted-foreground text-sm">
                    <Calendar className="w-4 h-4" />
                    Added {new Date(business.created_at || '').toLocaleDateString()}
                  </div>
                </CardContent>
              </Card>
              );
              })}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default BusinessDirectoryPage;
