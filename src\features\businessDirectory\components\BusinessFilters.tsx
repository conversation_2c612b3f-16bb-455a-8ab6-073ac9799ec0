import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapPin, Search, X, Shield, Filter, ArrowUpDown, Leaf, Building2, Calculator, TrendingUp, Star } from "lucide-react";
import { CategoryButton } from '@/components/ui/category-button';
import { getIndustryTheme, IndustryIcon } from '@/utils/industryIcons';
import { getNetZeroCategoryTheme, NetZeroCategoryIcon } from '@/utils/netZeroCategoryIcons';
import { NetZeroCategoryService } from '@/services/netZeroCategoryService';
import { UKIndustryService } from '@/services/ukIndustryService';
import BusinessLocationFilter from './BusinessLocationFilter';
import type { NetZeroCategoryWithSubcategories } from '@/types/netzero-categories.types';
import type { UKIndustryWithChildren } from '@/types/uk-industries.types';

// Sort options
export type BusinessSortOption = 'name-asc' | 'name-desc' | 'joined-asc' | 'joined-desc';

// Filter state interface
export interface BusinessFilterState {
  showFilters: boolean;
  searchTerm: string;
  verifiedOnly: boolean;
  officialPartnerOnly: boolean;
  selectedLocations: string[];
  selectedIndustries: string[];
  selectedNetZeroCategories: string[];
  sortBy: BusinessSortOption;
  // Sustainability filters
  carbonReductionPlan: boolean;
  financialRoiCalculation: boolean;
  carbonImpactCalculation: boolean;
}

interface BusinessFiltersProps {
  filters: BusinessFilterState;
  onFiltersChange: (filters: BusinessFilterState) => void;
  totalBusinesses: number;
  filteredCount: number;
}

const BusinessFilters: React.FC<BusinessFiltersProps> = ({
  filters,
  onFiltersChange,
  totalBusinesses,
  filteredCount
}) => {
  const [industries, setIndustries] = useState<UKIndustryWithChildren[]>([]);
  const [loadingIndustries, setLoadingIndustries] = useState(true);
  const [netZeroCategories, setNetZeroCategories] = useState<NetZeroCategoryWithSubcategories[]>([]);
  const [loadingNetZeroCategories, setLoadingNetZeroCategories] = useState(true);

  // Load filter data
  useEffect(() => {
    const loadIndustries = async () => {
      try {
        setLoadingIndustries(true);
        const industriesData = await UKIndustryService.getAllIndustriesWithChildren();
        setIndustries(industriesData);
      } catch (err) {
        console.error('Failed to load industries:', err);
      } finally {
        setLoadingIndustries(false);
      }
    };

    const loadNetZeroCategories = async () => {
      try {
        setLoadingNetZeroCategories(true);
        const categoriesData = await NetZeroCategoryService.getAllCategoriesWithSubcategories();
        setNetZeroCategories(categoriesData);
      } catch (err) {
        console.error('Failed to load net-zero categories:', err);
      } finally {
        setLoadingNetZeroCategories(false);
      }
    };

    loadIndustries();
    loadNetZeroCategories();
  }, []);

  // Filter helper functions
  const updateFilters = (updates: Partial<BusinessFilterState>) => {
    onFiltersChange({
      ...filters,
      ...updates
    });
  };

  const toggleIndustry = (industryId: string) => {
    updateFilters({
      selectedIndustries: filters.selectedIndustries.includes(industryId)
        ? filters.selectedIndustries.filter(id => id !== industryId)
        : [...filters.selectedIndustries, industryId]
    });
  };

  const toggleNetZeroCategory = (subcategoryId: string) => {
    updateFilters({
      selectedNetZeroCategories: filters.selectedNetZeroCategories.includes(subcategoryId)
        ? filters.selectedNetZeroCategories.filter(id => id !== subcategoryId)
        : [...filters.selectedNetZeroCategories, subcategoryId]
    });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      showFilters: false,
      searchTerm: '',
      verifiedOnly: false,
      officialPartnerOnly: false,
      selectedLocations: [],
      selectedIndustries: [],
      selectedNetZeroCategories: [],
      sortBy: 'joined-desc',
      carbonReductionPlan: false,
      financialRoiCalculation: false,
      carbonImpactCalculation: false
    });
  };

  const hasActiveFilters = () => {
    return filters.searchTerm ||
           filters.verifiedOnly ||
           filters.officialPartnerOnly ||
           filters.selectedLocations.length > 0 ||
           filters.selectedIndustries.length > 0 ||
           filters.selectedNetZeroCategories.length > 0 ||
           filters.carbonReductionPlan ||
           filters.financialRoiCalculation ||
           filters.carbonImpactCalculation;
  };

  // Render industry filter
  const renderIndustryFilter = () => {
    const selectAllIndustries = () => {
      const allIndustryIds = industries.flatMap(p => p.children?.map(c => c.id) || []);
      const allSelected = allIndustryIds.every(id => filters.selectedIndustries.includes(id));
      
      if (allSelected) {
        updateFilters({ selectedIndustries: [] });
      } else {
        updateFilters({ selectedIndustries: allIndustryIds });
      }
    };

    const totalSelected = filters.selectedIndustries.length;
    const totalAvailable = industries.reduce((total, parent) => total + (parent.children?.length || 0), 0);

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <Building2 size={16} />
            <Label className="text-sm font-semibold">Industries</Label>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {totalSelected} of {totalAvailable}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllIndustries}
              className="h-7 px-2 text-xs"
            >
              {totalSelected === totalAvailable ? 'Clear' : 'All'}
            </Button>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {industries.map((parentIndustry) => {
            const childrenInCategory = parentIndustry.children || [];
            const selectedInCategory = childrenInCategory.filter(child => 
              filters.selectedIndustries.includes(child.id)
            ).length;

            return (
              <CategoryButton
                key={parentIndustry.id}
                categoryId={parentIndustry.id}
                categoryName={parentIndustry.name}
                theme={getIndustryTheme(parentIndustry.name)}
                isSelected={selectedInCategory > 0}
                onClick={() => {
                  // Simple toggle: if any children are selected, clear all; otherwise select all
                  const allChildIds = childrenInCategory.map(child => child.id);

                  if (selectedInCategory > 0) {
                    // Clear all children in this category
                    updateFilters({
                      selectedIndustries: filters.selectedIndustries.filter(id =>
                        !allChildIds.includes(id)
                      )
                    });
                  } else {
                    // Select all children in this category
                    updateFilters({
                      selectedIndustries: [...filters.selectedIndustries, ...allChildIds]
                    });
                  }
                }}
                variant="dynamic-button"
                size="xs"
                showCount={false}
                className="w-fit"
              />
            );
          })}
        </div>
      </div>
    );
  };

  // Render net-zero categories filter
  const renderNetZeroCategoriesFilter = () => {
    const selectAllCategories = () => {
      const allSubcategoryIds = netZeroCategories.flatMap(c => c.subcategories?.map(s => s.id) || []);
      const allSelected = allSubcategoryIds.every(id => filters.selectedNetZeroCategories.includes(id));
      
      if (allSelected) {
        updateFilters({ selectedNetZeroCategories: [] });
      } else {
        updateFilters({ selectedNetZeroCategories: allSubcategoryIds });
      }
    };

    const totalSelected = filters.selectedNetZeroCategories.length;
    const totalAvailable = netZeroCategories.reduce((total, category) => total + (category.subcategories?.length || 0), 0);

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <Leaf size={16} />
            <Label className="text-sm font-semibold">Net Zero Categories</Label>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {totalSelected} of {totalAvailable}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={selectAllCategories}
              className="h-7 px-2 text-xs"
            >
              {totalSelected === totalAvailable ? 'Clear' : 'All'}
            </Button>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {netZeroCategories.map((category) => {
            const subcategoriesInCategory = category.subcategories || [];
            const selectedInCategory = subcategoriesInCategory.filter(subcategory => 
              filters.selectedNetZeroCategories.includes(subcategory.id)
            ).length;

            return (
              <CategoryButton
                key={category.id}
                categoryId={category.id}
                categoryName={category.name}
                theme={getNetZeroCategoryTheme(category.name)}
                isSelected={selectedInCategory > 0}
                onClick={() => {
                  // Simple toggle: if any subcategories are selected, clear all; otherwise select all
                  const allSubcategoryIds = subcategoriesInCategory.map(sub => sub.id);

                  if (selectedInCategory > 0) {
                    // Clear all subcategories in this category
                    updateFilters({
                      selectedNetZeroCategories: filters.selectedNetZeroCategories.filter(id =>
                        !allSubcategoryIds.includes(id)
                      )
                    });
                  } else {
                    // Select all subcategories in this category
                    updateFilters({
                      selectedNetZeroCategories: [...filters.selectedNetZeroCategories, ...allSubcategoryIds]
                    });
                  }
                }}
                variant="dynamic-button"
                size="xs"
                showCount={false}
                className="w-fit"
              />
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Search and Filter Controls */}
      <Card className="mb-6">
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search businesses by name, description, or website..."
                  value={filters.searchTerm}
                  onChange={(e) => updateFilters({ searchTerm: e.target.value })}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Quick Filters */}
            <div className="flex items-center gap-2 flex-wrap">
              {/* Verified Only Quick Filter */}
              <div className="flex items-center space-x-2 px-3 py-2 border rounded-md bg-background">
                <Checkbox
                  id="header-verified-only"
                  checked={filters.verifiedOnly}
                  onCheckedChange={(checked) => updateFilters({ verifiedOnly: !!checked })}
                />
                <Label htmlFor="header-verified-only" className="text-sm cursor-pointer flex items-center gap-1">
                  <Shield className="w-3 h-3 text-blue-600" />
                  <span className="hidden sm:inline">Verified Only</span>
                  <span className="sm:hidden">Verified</span>
                </Label>
              </div>

              {/* Official Partner Only Quick Filter */}
              <div className="flex items-center space-x-2 px-3 py-2 border rounded-md bg-background">
                <Checkbox
                  id="header-official-partner-only"
                  checked={filters.officialPartnerOnly}
                  onCheckedChange={(checked) => updateFilters({ officialPartnerOnly: !!checked })}
                />
                <Label htmlFor="header-official-partner-only" className="text-sm cursor-pointer flex items-center gap-1">
                  <Star className="w-3 h-3 text-yellow-600" />
                  <span className="hidden sm:inline">Official Partners</span>
                  <span className="sm:hidden">Partners</span>
                </Label>
              </div>

              {/* Carbon Reduction Plan Filter */}
              <div className="flex items-center space-x-2 px-4 py-2 border rounded-md bg-background min-w-[140px]">
                <Checkbox
                  id="header-carbon-reduction-plan"
                  checked={filters.carbonReductionPlan}
                  onCheckedChange={(checked) => updateFilters({ carbonReductionPlan: !!checked })}
                />
                <Label htmlFor="header-carbon-reduction-plan" className="text-sm cursor-pointer flex items-center gap-1">
                  <Leaf className="w-3 h-3 text-green-600" />
                  <span className="hidden sm:inline">Carbon Plan</span>
                  <span className="sm:hidden">Carbon</span>
                </Label>
              </div>

              {/* Financial ROI Filter */}
              <div className="flex items-center space-x-2 px-4 py-2 border rounded-md bg-background min-w-[140px]">
                <Checkbox
                  id="header-financial-roi-calculation"
                  checked={filters.financialRoiCalculation}
                  onCheckedChange={(checked) => updateFilters({ financialRoiCalculation: !!checked })}
                />
                <Label htmlFor="header-financial-roi-calculation" className="text-sm cursor-pointer flex items-center gap-1">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                  <span className="hidden sm:inline">Financial ROI</span>
                  <span className="sm:hidden">ROI</span>
                </Label>
              </div>

              {/* Carbon Impact Calculation Filter */}
              <div className="flex items-center space-x-2 px-4 py-2 border rounded-md bg-background min-w-[140px]">
                <Checkbox
                  id="header-carbon-impact-calculation"
                  checked={filters.carbonImpactCalculation}
                  onCheckedChange={(checked) => updateFilters({ carbonImpactCalculation: !!checked })}
                />
                <Label htmlFor="header-carbon-impact-calculation" className="text-sm cursor-pointer flex items-center gap-1">
                  <Calculator className="w-3 h-3 text-green-600" />
                  <span className="hidden sm:inline">Carbon Impact</span>
                  <span className="sm:hidden">Impact</span>
                </Label>
              </div>
            </div>

            {/* Sort Control */}
            <div className="flex items-center gap-2">
              <ArrowUpDown className="w-4 h-4 text-muted-foreground" />
              <Select value={filters.sortBy} onValueChange={(value: BusinessSortOption) => updateFilters({ sortBy: value })}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name-asc">Name A-Z</SelectItem>
                  <SelectItem value="name-desc">Name Z-A</SelectItem>
                  <SelectItem value="joined-desc">Newest First</SelectItem>
                  <SelectItem value="joined-asc">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Results and Filter Toggle */}
            <div className="flex items-center justify-between lg:justify-end gap-4">
              <div className="text-sm text-muted-foreground">
                Showing {filteredCount} of {totalBusinesses} businesses
              </div>
              <Button
                variant={filters.showFilters ? "default" : "outline"}
                size="sm"
                onClick={() => updateFilters({ showFilters: !filters.showFilters })}
                className={`${filters.showFilters ? "bg-blue-600 hover:bg-blue-700" : ""} min-w-[120px]`}
              >
                <Filter className="w-4 h-4 mr-2" />
                <span className="hidden xs:inline">{filters.showFilters ? 'Hide' : 'Show'} </span>Filters
                {hasActiveFilters() && (
                  <Badge variant="secondary" className="ml-2 bg-white text-blue-600 text-xs">
                    {(filters.verifiedOnly ? 1 : 0) +
                     (filters.officialPartnerOnly ? 1 : 0) +
                     filters.selectedLocations.length +
                     filters.selectedIndustries.length +
                     filters.selectedNetZeroCategories.length +
                     (filters.carbonReductionPlan ? 1 : 0) +
                     (filters.financialRoiCalculation ? 1 : 0) +
                     (filters.carbonImpactCalculation ? 1 : 0)}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Filter Panels */}
        {filters.showFilters && (
          <CardContent className="pt-0">
            <div className="space-y-4">
              {/* Location Filter */}
              <Card className="p-3 lg:p-4">
                <BusinessLocationFilter
                  selectedLocations={filters.selectedLocations}
                  onLocationsChange={(locations) => updateFilters({ selectedLocations: locations })}
                  title="Location"
                  subtitle="Filter by business headquarters locations"
                />
              </Card>

              {/* Complex Filters */}
              <div className="grid gap-4 lg:grid-cols-2">
                {/* Net-Zero Categories Filter */}
                {!loadingNetZeroCategories && (
                  <Card className="p-3 lg:p-4 min-w-0">
                    {renderNetZeroCategoriesFilter()}
                  </Card>
                )}

                {/* Industry Filter */}
                {!loadingIndustries && (
                  <Card className="p-3 lg:p-4 min-w-0">
                    {renderIndustryFilter()}
                  </Card>
                )}
              </div>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters() && (
              <div className="mt-4 pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-destructive hover:text-destructive"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear All Filters
                </Button>
              </div>
            )}
          </CardContent>
        )}
      </Card>

      {/* Active Filters Display */}
      {hasActiveFilters() && (
        <Card className="mb-6">
          <CardContent className="py-4">
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm font-medium text-muted-foreground">Active filters:</span>
              
              {/* Search term filter */}
              {filters.searchTerm && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Search className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Search: {filters.searchTerm}</span>
                  <button 
                    onClick={() => updateFilters({ searchTerm: '' })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Verified filter */}
              {filters.verifiedOnly && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Shield className="w-3 h-3 text-blue-600 flex-shrink-0" />
                  <span className="whitespace-nowrap">Verified Only</span>
                  <button
                    onClick={() => updateFilters({ verifiedOnly: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Official Partner filter */}
              {filters.officialPartnerOnly && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Star className="w-3 h-3 text-yellow-600 flex-shrink-0" />
                  <span className="whitespace-nowrap">Official Partners</span>
                  <button
                    onClick={() => updateFilters({ officialPartnerOnly: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Location filters */}
              {filters.selectedLocations.length > 0 && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit bg-gray-100 text-gray-700 border-gray-300">
                  <MapPin className="w-3 h-3 text-blue-600 flex-shrink-0" />
                  <span className="whitespace-nowrap">{filters.selectedLocations.length} Location{filters.selectedLocations.length !== 1 ? 's' : ''}</span>
                  <button
                    onClick={() => updateFilters({ selectedLocations: [] })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {/* Industry filters */}
              {filters.selectedIndustries.map((industryId) => {
                const industry = industries.flatMap(p => p.children || []).find(c => c.id === industryId);
                const parentIndustry = industries.find(p => p.children?.some(c => c.id === industryId));
                const theme = parentIndustry ? getIndustryTheme(parentIndustry.name) : null;
                return industry ? (
                  <Badge
                    key={industryId}
                    variant="outline"
                    className={`flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit border-2 ${theme?.selectedBgColor || 'bg-gray-50'} ${theme?.iconColor || 'text-gray-700'}`}
                    style={theme?.topBorderStyle}
                  >
                    <div className="flex items-center gap-1">
                      <IndustryIcon
                        industryName={parentIndustry?.name || industry.name}
                        className="w-3 h-3 flex-shrink-0"
                      />
                      <span className="whitespace-nowrap">{industry.name}</span>
                    </div>
                    <button
                      onClick={() => toggleIndustry(industryId)}
                      className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ) : null;
              })}

              {/* Net-zero category filters */}
              {filters.selectedNetZeroCategories.map((subcategoryId) => {
                const subcategory = netZeroCategories.flatMap(c => c.subcategories || []).find(s => s.id === subcategoryId);
                const parentCategory = netZeroCategories.find(c => c.subcategories?.some(s => s.id === subcategoryId));
                const theme = parentCategory ? getNetZeroCategoryTheme(parentCategory.name) : null;
                return subcategory ? (
                  <Badge
                    key={subcategoryId}
                    variant="outline"
                    className={`flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit border-2 ${theme?.selectedBgColor || 'bg-gray-50'} ${theme?.iconColor || 'text-gray-700'}`}
                    style={theme?.topBorderStyle}
                  >
                    <div className="flex items-center gap-1">
                      <NetZeroCategoryIcon
                        categoryName={parentCategory?.name || subcategory.name}
                        className="w-3 h-3 flex-shrink-0"
                      />
                      <span className="whitespace-nowrap">{subcategory.name}</span>
                    </div>
                    <button
                      onClick={() => toggleNetZeroCategory(subcategoryId)}
                      className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ) : null;
              })}

              {/* Sustainability filters */}
              {filters.carbonReductionPlan && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Leaf className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Carbon Reduction Plan</span>
                  <button 
                    onClick={() => updateFilters({ carbonReductionPlan: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {filters.financialRoiCalculation && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <TrendingUp className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Financial ROI Calculation</span>
                  <button 
                    onClick={() => updateFilters({ financialRoiCalculation: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}

              {filters.carbonImpactCalculation && (
                <Badge variant="secondary" className="flex items-center gap-1 text-xs rounded-md px-3 py-1.5 w-fit">
                  <Calculator className="w-3 h-3 flex-shrink-0" />
                  <span className="whitespace-nowrap">Carbon Impact Calculation</span>
                  <button 
                    onClick={() => updateFilters({ carbonImpactCalculation: false })}
                    className="ml-1 hover:bg-gray-300 rounded-md p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default BusinessFilters; 