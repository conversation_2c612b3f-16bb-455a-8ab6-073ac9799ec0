import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CategoryButton } from '@/components/ui/category-button';
import { getIndustryTheme } from '@/utils/industryIcons';

export const IndustryCategoryCardsDemoStyle: React.FC<{
  industries: Array<{ id: string; name: string; children: Array<{ id: string; name: string }> }>;
  items: any[]; // events, funding, or businesses
  selectedIndustryId?: string;
  onIndustrySelect?: (industryId: string, industryName: string) => void;
  type: 'events' | 'funding' | 'business';
}> = ({ industries, items, selectedIndustryId, onIndustrySelect, type }) => {
  // Safety check
  if (!industries || !Array.isArray(industries)) {
    return null;
  }

  // For each parent industry, count unique items that have that parent or any child
  const industryCounts = industries.map(parent => {
    // Get all child IDs for this parent
    const allIds = [parent.id, ...(parent.children || []).map(c => c.id)];
    // Count unique items that match any of these IDs
    const count = items.filter(item => {
      if (type === 'business') {
        // Business: check mainIndustry and targetIndustries
        const main = item.mainIndustry?.id;
        const targets = item.targetIndustries?.map((i: any) => i.id) || [];
        return allIds.includes(main) || targets.some(id => allIds.includes(id));
      } else if (type === 'events') {
        // Events: check relevant_industries
        return (item.relevant_industries || []).some((ind: any) => allIds.includes(ind.industry_id));
      } else if (type === 'funding') {
        // Funding: check relevant_industries
        return (item.relevant_industries || []).some((ind: any) => allIds.includes(ind.industry_id));
      }
      return false;
    }).length;
    return { ...parent, count };
  });

  // Add Not Classified card if needed
  const notClassifiedCount = items.filter(item => {
    if (type === 'business') {
      const main = item.mainIndustry?.id;
      const targets = item.targetIndustries?.map((i: any) => i.id) || [];
      return !main && (!targets || targets.length === 0);
    } else if (type === 'events' || type === 'funding') {
      return !item.relevant_industries || item.relevant_industries.length === 0;
    }
    return false;
  }).length;
  const allIndustryCards = [...industryCounts];
  if (notClassifiedCount > 0) {
    allIndustryCards.push({
      id: 'not-classified',
      name: 'Not Classified',
      count: notClassifiedCount
    });
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Industry Categories</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {allIndustryCards.map((industry) => (
            <CategoryButton
              key={industry.id}
              categoryId={industry.id}
              categoryName={industry.name}
              count={industry.count}
              theme={getIndustryTheme(industry.name)}
              variant="card"
              size="md"
              showCount={true}
              isSelected={selectedIndustryId === industry.id}
              onClick={onIndustrySelect}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}; 